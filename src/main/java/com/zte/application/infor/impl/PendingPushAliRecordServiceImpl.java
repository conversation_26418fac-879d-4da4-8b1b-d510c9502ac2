package com.zte.application.infor.impl;

import com.zte.application.infor.PendingPushAliRecordService;
import com.zte.domain.model.infor.InventoryDiffQueryRepository;
import com.zte.domain.model.infor.PendingPushAliRecordRepository;
import com.zte.interfaces.infor.dto.PendingQualityCheckTaskDTO;
import com.zte.interfaces.infor.dto.PendingQualityCheckTaskListVO;
import com.zte.interfaces.step.dto.ZteWarehouseInfoDTO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 待推送阿里记录服务实现类
 */
@Service
public class PendingPushAliRecordServiceImpl implements PendingPushAliRecordService {

    @Autowired
    private PendingPushAliRecordRepository pendingPushAliRecordRepository;

    @Autowired
    private InventoryDiffQueryRepository inventoryDiffQueryRepository;

    @Override
    public PendingQualityCheckTaskListVO getPendingQualityCheckTaskList(PendingQualityCheckTaskDTO dto) {
        PendingQualityCheckTaskListVO listVo = new PendingQualityCheckTaskListVO();

        // 查询所有的库房
        List<ZteWarehouseInfoDTO> zteWarehouseList = inventoryDiffQueryRepository.getInforWarehouseList();

        if (StringUtils.isNotBlank(dto.getWhseid())) {
            zteWarehouseList = zteWarehouseList.stream().filter(x -> dto.getWhseid().equals(x.getWarehouseId())).collect(Collectors.toList());
        }

        if (CollectionUtils.isEmpty(zteWarehouseList)) {
            return listVo;
        }

        dto.setStartRow((dto.getPageIndex() - 1) * dto.getPageSize() + 1).setEndRow(dto.getPageIndex() * dto.getPageSize());
        listVo.setTotal(pendingPushAliRecordRepository.getPendingQualityCheckTaskListTotal(zteWarehouseList));
        listVo.setPendingQualityCheckTaskDTOList(pendingPushAliRecordRepository.getPendingQualityCheckTaskList(zteWarehouseList, dto));
        return listVo;
    }

}
