package com.zte.application.infor.impl;

import cn.afterturn.easypoi.handler.inter.IExcelExportServer;
import com.zte.application.infor.WarehouseAlgorithmService;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.common.utils.NumConstant;
import com.zte.common.utils.ThreadUtil;
import com.zte.domain.model.infor.InventoryholdRecordRepository;
import com.zte.domain.model.infor.WarehouseAlgorithmRepository;
import com.zte.interfaces.infor.dto.*;
import com.zte.interfaces.infor.vo.*;
import com.zte.resourcewarehouse.common.utils.BusiAssertException;
import com.zte.resourcewarehouse.common.utils.ExcelUtil;
import com.zte.resourcewarehouse.common.utils.Tools;
import com.zte.resourcewarehouse.common.vo.ExcelParams;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.zte.common.utils.Constant.*;
import static com.zte.common.utils.NumConstant.INT_0;


/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class WarehouseAlgorithmServiceImpl implements WarehouseAlgorithmService {

    private static final Logger LOGGER = LoggerFactory.getLogger(WarehouseAlgorithmServiceImpl.class);

    @Autowired
    private InventoryholdRecordRepository inventoryholdRecordRepository;
    @Autowired
    private WarehouseAlgorithmRepository warehouseAlgorithmRepository;

    /**
     * 查询方案类型列表
     */
    @Override
    public List<SysLookupValuesDTO> getSchemeTypeList() {
        SysLookupValuesDTO sysLookupValuesDTO = SysLookupValuesDTO.builder().build().setLookupType(LOOKUP_TYPE_1000060);
        return inventoryholdRecordRepository.getLookupValues(sysLookupValuesDTO);
    }

    /**
     * 查询整库策略列表
     */
    @Override
    public List<SysLookupValuesDTO> getWholeWarehouseStrategyList() {
        SysLookupValuesDTO sysLookupValuesDTO = SysLookupValuesDTO.builder().build().setLookupType(LOOKUP_TYPE_1000061);
        return inventoryholdRecordRepository.getLookupValues(sysLookupValuesDTO);
    }

    /**
     * 查询算法方案
     */
    @Override
    public WarehouseAlgorithmListVO getWarehouseAlgorithmList(WarehouseAlgorithmDTO dto) {
        WarehouseAlgorithmListVO listVo = new WarehouseAlgorithmListVO();
        dto.setStartRow((dto.getPageIndex() - 1) * dto.getPageSize() + 1).setEndRow(dto.getPageIndex() * dto.getPageSize());
        listVo.setTotal(warehouseAlgorithmRepository.getWarehouseAlgorithmListVOTotal(dto));
        listVo.setWarehouseAlgorithmDTOList(warehouseAlgorithmRepository.getWarehouseAlgorithmList(dto));
        return listVo;
    }

    /**
     * 保存算法方案
     */
    @Override
    public void saveWarehouseAlgorithm(WarehouseAlgorithmDTO dto) {
        if (Tools.isEmpty(dto.getSerialkey())) {
            // 校验保存的算法方案是否已存在
            WarehouseAlgorithmDTO warehouseAlgorithmDTO = WarehouseAlgorithmDTO.builder().build()
                    .setWhseid(dto.getWhseid()).setWarehouse(dto.getWarehouse()).setWarehouseArea(dto.getWarehouseArea())
                    .setAlgorithmScheme(dto.getAlgorithmScheme()).setEnabledFlag(FLAG_Y);
            BusiAssertException.isTrue(warehouseAlgorithmRepository.getWarehouseAlgorithmListVOTotal(warehouseAlgorithmDTO)>INT_0,
                    MessageId.WAREHOUSE_ALGORITHM_EXISTS);
        }
        // 字段校验
        WarehouseAlgorithmDTO warehouseAlgorithmDTO = WarehouseAlgorithmDTO.builder().build()
                .setAlgorithmScheme(dto.getAlgorithmScheme()).setEnabledFlag(FLAG_Y);
        List<WarehouseAlgorithmDTO> list = warehouseAlgorithmRepository.getWarehouseAlgorithmList(warehouseAlgorithmDTO);
        List<WarehouseAlgorithmDTO> warehouseAlgorithmDTOS = list.stream().filter(i ->
                Tools.notEquals(i.getSerialkey(), dto.getSerialkey())).collect(Collectors.toList());
        if (Tools.isNotEmpty(warehouseAlgorithmDTOS)) {
            checkAlgorithmExists(dto, warehouseAlgorithmDTOS.get(INT_0));
        }
        warehouseAlgorithmRepository.saveWarehouseAlgorithm(dto);
    }

    /**
     * 校验导入的算法，与数据库已存在算法，算法属性是否一致
     */
    public void checkAlgorithmExists(WarehouseAlgorithmDTO dto, WarehouseAlgorithmDTO existsDto) {
        // 方案类型不一致
        BusiAssertException.notEquals(dto.getSchemeType(), existsDto.getSchemeType(),
                MessageId.ALGORITHM_SCHEME_TYPE_INCONSISTENT_EXISTS);
        // 整库策略不一致
        BusiAssertException.notEquals(dto.getWholeWarehouseStrategy(), existsDto.getWholeWarehouseStrategy(),
                MessageId.ALGORITHM_WHOLE_WAREHOUSE_STRATEGY_INCONSISTENT_EXISTS);
        // 单位整库工作量不一致
        BusiAssertException.notEquals(dto.getUnitWholeWarehouseWork(), existsDto.getUnitWholeWarehouseWork(),
                MessageId.ALGORITHM_UNIT_WHOLE_WAREHOUSE_WORK_INCONSISTENT_EXISTS);
        // 起点x坐标不一致
        BusiAssertException.notEquals(dto.getStartCoordX(), existsDto.getStartCoordX(),
                MessageId.ALGORITHM_START_COORD_X_INCONSISTENT_EXISTS);
        // 起点y坐标不一致
        BusiAssertException.notEquals(dto.getStartCoordY(), existsDto.getStartCoordY(),
                MessageId.ALGORITHM_START_COORD_Y_INCONSISTENT_EXISTS);
        // 终点x坐标不一致
        BusiAssertException.notEquals(dto.getEndCoordX(), existsDto.getEndCoordX(),
                MessageId.ALGORITHM_END_COORD_X_INCONSISTENT_EXISTS);
        // 终点y坐标不一致
        BusiAssertException.notEquals(dto.getEndCoordY(), existsDto.getEndCoordY(),
                MessageId.ALGORITHM_END_COORD_Y_INCONSISTENT_EXISTS);
        // 执行频率不一致
        BusiAssertException.notEquals(dto.getExecutionFrequency(), existsDto.getExecutionFrequency(),
                MessageId.ALGORITHM_EXECUTION_FREQUENCY_INCONSISTENT_EXISTS);
        //整库人力不一致
        BusiAssertException.notEquals(dto.getWholeWarehouseManpower(), existsDto.getWholeWarehouseManpower(),
                MessageId.ALGORITHM_EXECUTION_MANPOWER_INCONSISTENT);
        //最小整库单元不一致
        BusiAssertException.notEquals(dto.getMinWholeWarehouseUnit(), existsDto.getMinWholeWarehouseUnit(),
                MessageId.ALGORITHM_EXECUTION_UNIT_INCONSISTENT);
    }

    /**
     * 更新算法方案
     */
    @Override
    public void updateWarehouseAlgorithm(WarehouseAlgorithmDTO dto) {
        // 字段校验
        if (Tools.notEquals(FLAG_N, dto.getEnabledFlag())) {
            WarehouseAlgorithmDTO warehouseAlgorithmDTO = WarehouseAlgorithmDTO.builder().build()
                    .setAlgorithmScheme(dto.getAlgorithmScheme()).setEnabledFlag(FLAG_Y);
            List<WarehouseAlgorithmDTO> list = warehouseAlgorithmRepository.getWarehouseAlgorithmList(warehouseAlgorithmDTO);
            List<WarehouseAlgorithmDTO> warehouseAlgorithmDTOS = list.stream().filter(i ->
                    Tools.notEquals(i.getSerialkey(), dto.getSerialkey())).collect(Collectors.toList());
            if (Tools.isNotEmpty(warehouseAlgorithmDTOS)) {
                checkAlgorithmExists(dto, warehouseAlgorithmDTOS.get(INT_0));
            }
        }
        warehouseAlgorithmRepository.updateWarehouseAlgorithm(dto);
    }

    /**
     * 导入算法方案
     */
    @Override
    public void importWarehouseAlgorithm(List<WarehouseAlgorithmDTO> list) {
        if (Tools.isEmpty(list)) {
            return;
        }
        // 校验导入的算法方案是否重复
        List<String> algorithmSchemeList = list.stream().map(i -> i.getWhseid()+i.getWarehouse()+i.getWarehouseArea()
                        +i.getAlgorithmScheme()).distinct().collect(Collectors.toList());
        BusiAssertException.isTrue(list.size() != algorithmSchemeList.size(),
                MessageId.WAREHOUSE_ALGORITHM_REPEATED);
        // 校验导入的算法方案是否已存在
        BusiAssertException.isTrue(warehouseAlgorithmRepository.getWarehouseAlgorithmCount(list)>INT_0,
                MessageId.WAREHOUSE_ALGORITHM_EXISTS);
        // 字段校验
        List<String> algorithmList = list.stream().map(WarehouseAlgorithmDTO::getAlgorithmScheme).distinct().collect(Collectors.toList());
        // 校验导入的算法，属性是否一致
        for (String algorithm : algorithmList) {
            List<WarehouseAlgorithmDTO> algorithmDTOList = list.stream().filter(i ->
                    Tools.equals(algorithm, i.getAlgorithmScheme())).collect(Collectors.toList());
            checkAlgorithm(algorithmDTOList);
        }
        WarehouseAlgorithmDTO warehouseAlgorithmDTO = WarehouseAlgorithmDTO.builder().build()
                .setAlgorithmSchemeList(algorithmList).setEnabledFlag(FLAG_Y);
        List<WarehouseAlgorithmDTO> warehouseAlgorithmDTOList = warehouseAlgorithmRepository.getWarehouseAlgorithmList(warehouseAlgorithmDTO);
        // 校验导入的算法，与数据库已存在算法，算法属性是否一致
        for (WarehouseAlgorithmDTO dto : list) {
            List<WarehouseAlgorithmDTO> warehouseAlgorithmDTOS = warehouseAlgorithmDTOList.stream().filter(i ->
                    i.getAlgorithmScheme().equals(dto.getAlgorithmScheme())).collect(Collectors.toList());
            if (Tools.isNotEmpty(warehouseAlgorithmDTOS)) {
                checkAlgorithmExists(dto, warehouseAlgorithmDTOS.get(INT_0));
            }
        }
        warehouseAlgorithmRepository.saveWarehouseAlgorithmList(list);
    }

    /**
     * 校验导入的算法，属性是否一致
     */
    public void checkAlgorithm(List<WarehouseAlgorithmDTO> algorithmDTOList) {
        // 方案类型不一致
        List<String> schemeTypeList = algorithmDTOList.stream().map(WarehouseAlgorithmDTO::getSchemeType)
                .distinct().collect(Collectors.toList());
        BusiAssertException.isTrue(schemeTypeList.size() > NumConstant.INT_1,
                MessageId.ALGORITHM_SCHEME_TYPE_INCONSISTENT);
        // 整库策略不一致
        List<String> wholeWarehouseStrategyList = algorithmDTOList.stream().map(WarehouseAlgorithmDTO::getWholeWarehouseStrategy)
                .distinct().collect(Collectors.toList());
        BusiAssertException.isTrue(wholeWarehouseStrategyList.size() > NumConstant.INT_1,
                MessageId.ALGORITHM_WHOLE_WAREHOUSE_STRATEGY_INCONSISTENT);
        // 单位整库工作量不一致
        List<BigDecimal> unitWholeWarehouseWorkList = algorithmDTOList.stream().map(WarehouseAlgorithmDTO::getUnitWholeWarehouseWork)
                .distinct().collect(Collectors.toList());
        BusiAssertException.isTrue(unitWholeWarehouseWorkList.size() > NumConstant.INT_1,
                MessageId.ALGORITHM_UNIT_WHOLE_WAREHOUSE_WORK_INCONSISTENT);
        // 起点x坐标不一致
        List<BigDecimal> startCoordXList = algorithmDTOList.stream().map(WarehouseAlgorithmDTO::getStartCoordX)
                .distinct().collect(Collectors.toList());
        BusiAssertException.isTrue(startCoordXList.size() > NumConstant.INT_1,
                MessageId.ALGORITHM_START_COORD_X_INCONSISTENT);
        // 起点y坐标不一致
        List<BigDecimal> startCoordYList = algorithmDTOList.stream().map(WarehouseAlgorithmDTO::getStartCoordY)
                .distinct().collect(Collectors.toList());
        BusiAssertException.isTrue(startCoordYList.size() > NumConstant.INT_1,
                MessageId.ALGORITHM_START_COORD_Y_INCONSISTENT);
        // 终点x坐标不一致
        List<BigDecimal> endCoordXList = algorithmDTOList.stream().map(WarehouseAlgorithmDTO::getEndCoordX)
                .distinct().collect(Collectors.toList());
        BusiAssertException.isTrue(endCoordXList.size() > NumConstant.INT_1,
                MessageId.ALGORITHM_END_COORD_X_INCONSISTENT);
        // 终点y坐标不一致
        List<BigDecimal> endCoordYList = algorithmDTOList.stream().map(WarehouseAlgorithmDTO::getEndCoordY)
                .distinct().collect(Collectors.toList());
        BusiAssertException.isTrue(endCoordYList.size() > NumConstant.INT_1,
                MessageId.ALGORITHM_END_COORD_Y_INCONSISTENT);
        // 执行频率不一致
        List<BigDecimal> executionFrequencyList = algorithmDTOList.stream().map(WarehouseAlgorithmDTO::getExecutionFrequency)
                .distinct().collect(Collectors.toList());
        BusiAssertException.isTrue(executionFrequencyList.size() > NumConstant.INT_1,
                MessageId.ALGORITHM_EXECUTION_FREQUENCY_INCONSISTENT);
        // 整库人力不一致
        List<BigDecimal> wholeWarehouseManpowerList = algorithmDTOList.stream().map(WarehouseAlgorithmDTO::getWholeWarehouseManpower)
                .distinct().collect(Collectors.toList());
        BusiAssertException.isTrue(wholeWarehouseManpowerList.size() > NumConstant.INT_1,
                MessageId.ALGORITHM_EXECUTION_MANPOWER_INCONSISTENT);
        // 最小整库单元不一致
        List<BigDecimal> minWholeWarehouseUnitList = algorithmDTOList.stream().map(WarehouseAlgorithmDTO::getMinWholeWarehouseUnit)
                .distinct().collect(Collectors.toList());
        BusiAssertException.isTrue(minWholeWarehouseUnitList.size() > NumConstant.INT_1,
                MessageId.ALGORITHM_EXECUTION_UNIT_INCONSISTENT);
    }

    /**
     * 导出算法方案
     */
    @Override
    public void exportWarehouseAlgorithm(WarehouseAlgorithmDTO dto) {
        int total = warehouseAlgorithmRepository.getWarehouseAlgorithmListVOTotal(dto);
        IExcelExportServer server = (params, page) -> {
            int statRow = (page - NumConstant.INT_1) * INT_5000 + NumConstant.INT_1;
            int endRow = page * INT_5000;
            WarehouseAlgorithmDTO param = ((WarehouseAlgorithmDTO) params).setStartRow(statRow).setEndRow(endRow);
            return new ArrayList<>(warehouseAlgorithmRepository.getWarehouseAlgorithmList(param));
        };
        ExcelParams excelParams = ExcelParams.builder().build()
                .setTitle(WAREHOUSE_ALGORITHM_NAME).setFileName(WAREHOUSE_ALGORITHM_EXPORT).setSheetName(SHEET1)
                .setQueryParams(dto).setPojoClass(WarehouseAlgorithmDTO.class).setTotal((long) total)
                .setReceipt(dto.getEmpNo()).setCreateHeadRows(true)
                .setExcelExportServer(server);
        ExcelUtil.exportExcel(excelParams);
    }

    /**
     * 查询算法计算结果
     */
    @Override
    public AlgorithmResultHeadListVO getAlgorithmResultHeadList(AlgorithmResultDetailDTO dto) {
        AlgorithmResultHeadListVO listVo = new AlgorithmResultHeadListVO();
        dto.setStartRow((dto.getPageIndex() - 1) * dto.getPageSize() + 1).setEndRow(dto.getPageIndex() * dto.getPageSize());
        listVo.setTotal(warehouseAlgorithmRepository.getAlgorithmResultHeadListVOTotal(dto));
        listVo.setAlgorithmResultHeadDTOList(warehouseAlgorithmRepository.getAlgorithmResultHeadList(dto));
        return listVo;
    }

    /**
     * 查询算法计算结果明细
     */
    @Override
    public AlgorithmResultDetailListVO getAlgorithmResultDetailList(AlgorithmResultDetailDTO dto) {
        AlgorithmResultDetailListVO listVo = new AlgorithmResultDetailListVO();
        dto.setStartRow((dto.getPageIndex() - 1) * dto.getPageSize() + 1).setEndRow(dto.getPageIndex() * dto.getPageSize());
        listVo.setTotal(warehouseAlgorithmRepository.getAlgorithmResultDetailListVOTotal(dto));
        listVo.setAlgorithmResultDetailDTOList(warehouseAlgorithmRepository.getAlgorithmResultDetailList(dto));
        AlgorithmResultDetailDTO algorithmResultPrintDTO = new AlgorithmResultDetailDTO();
        BeanUtils.copyProperties(dto, algorithmResultPrintDTO);
        algorithmResultPrintDTO.setStartRow(null).setEndRow(null);
        listVo.setAllAlgorithmResultDetailDTOList(warehouseAlgorithmRepository.getAlgorithmResultDetailList(algorithmResultPrintDTO));
        return listVo;
    }

    /**
     * 导出算法计算结果
     */
    @Override
    public void exportAlgorithmResult(AlgorithmResultDetailDTO dto) {
        int total = warehouseAlgorithmRepository.getAlgorithmResultDetailListVOTotal(dto);
        IExcelExportServer server = (params, page) -> {
            int statRow = (page - NumConstant.INT_1) * INT_5000 + NumConstant.INT_1;
            int endRow = page * INT_5000;
            AlgorithmResultDetailDTO param = ((AlgorithmResultDetailDTO) params).setStartRow(statRow).setEndRow(endRow);
            return new ArrayList<>(warehouseAlgorithmRepository.getAlgorithmResultDetailList(param));
        };
        ExcelParams excelParams = ExcelParams.builder().build()
                .setTitle(ALGORITHM_RESULT_NAME).setFileName(ALGORITHM_RESULT_EXPORT).setSheetName(SHEET1)
                .setQueryParams(dto).setPojoClass(AlgorithmResultDetailDTO.class).setTotal((long) total)
                .setReceipt(dto.getEmpNo()).setCreateHeadRows(true)
                .setExcelExportServer(server);
        ExcelUtil.exportExcel(excelParams);
    }

    /* Started by AICoder, pid:f86f3a297fa650c1497f09b130c69c1eb527549d */
    /**
     * 查询算法执行日志
     */
    @Override
    public AlgorithmExecuteLogListVO getAlgorithmExecuteLogList(AlgorithmExecuteLogDTO dto) {
        AlgorithmExecuteLogListVO listVo = new AlgorithmExecuteLogListVO();
        dto.setStartRow((dto.getPageIndex() - 1) * dto.getPageSize() + 1).setEndRow(dto.getPageIndex() * dto.getPageSize());
        listVo.setTotal(warehouseAlgorithmRepository.getAlgorithmExecuteLogListVOTotal(dto));
        listVo.setAlgorithmExecuteLogDTOList(warehouseAlgorithmRepository.getAlgorithmExecuteLogList(dto));
        return listVo;
    }
    /* Ended by AICoder, pid:f86f3a297fa650c1497f09b130c69c1eb527549d */

    /**
     * 查询算法计算任务明细
     */
    @Override
    public AlgorithmTaskDetailListVO getAlgorithmTaskDetailList(AlgorithmResultDetailDTO dto) {
        AlgorithmTaskDetailListVO listVo = new AlgorithmTaskDetailListVO();
        List<AlgorithmTaskDetailDTO> algorithmTaskDetailDTOList = new ArrayList<>();
        algorithmTaskDetailDTOList = warehouseAlgorithmRepository.getAlgorithmTaskDetailList(dto);
        //如果没有查到再查结果明细表并保存到任务表
        if (CollectionUtils.isEmpty(algorithmTaskDetailDTOList)) {
            algorithmTaskDetailDTOList = warehouseAlgorithmRepository.getAlgorithmTaskList(dto);
            if (CollectionUtils.isEmpty(algorithmTaskDetailDTOList)) {
                listVo.setTotal(Constant.INT_0);
                return listVo.setAlgorithmTaskDetailDtos(new ArrayList<>());
            }
            warehouseAlgorithmRepository.insertAlgorithmTaskDetailList(algorithmTaskDetailDTOList);
        }
        if (dto.getPageIndex() == null || dto.getPageSize() == null) {
            return listVo.setAlgorithmTaskDetailDtos(warehouseAlgorithmRepository.getAlgorithmTaskDetailList(dto));
        }
        dto.setStartRow((dto.getPageIndex() - 1) * dto.getPageSize() + 1).setEndRow(dto.getPageIndex() * dto.getPageSize());
        listVo.setTotal(warehouseAlgorithmRepository.getAlgorithmTaskDetailListTotal(dto));
        listVo.setAlgorithmTaskDetailDtos(warehouseAlgorithmRepository.getAlgorithmTaskDetailList(dto));
        return listVo;
    }


    @Override
    public AlgorithmTaskDetailListVO printAlgorithmTaskInfo(AlgorithmResultDetailDTO dto) {
        AlgorithmTaskDetailListVO listVo = new AlgorithmTaskDetailListVO();
        List<AlgorithmResultDetailDTO> algorithmResultDetailList = warehouseAlgorithmRepository.getAlgorithmResultDetailList(dto);
        if (CollectionUtils.isEmpty(algorithmResultDetailList)) {
            return listVo.setAlgorithmTaskDetailDtos(new ArrayList<>());
        }
        //判断明细是否移动,
        if (!hasNonNewStatus(algorithmResultDetailList)) {
            //开启异步更新LPN明细信息
            ThreadUtil.AI_GET_LPN_INVENTORY.execute(() -> {
                log.info("AI_GET_LPN_INVENTORY---begin");
                List<String> lpnList = new ArrayList<>();
                //通过infor仓库+物料代码+自库位获取infor库存信息
                for (AlgorithmResultDetailDTO algorithmResultDetailDTO : algorithmResultDetailList) {
                    lpnList = warehouseAlgorithmRepository.getInforInventoryInfo(algorithmResultDetailDTO);
                    warehouseAlgorithmRepository.deleteTaskDetailInfo(algorithmResultDetailDTO);
                    if (!CollectionUtils.isEmpty(lpnList)) {
                        saveResultDetailLpnInfo(lpnList, algorithmResultDetailDTO);
                    }
                }
                log.info("AI_GET_LPN_INVENTORY---end");
            });
        }
        listVo.setAlgorithmTaskDetailDtos(warehouseAlgorithmRepository.getAlgorithmTaskDetailList(dto));
        return listVo;
    }


    public boolean hasNonNewStatus(List<AlgorithmResultDetailDTO> algorithmResultDetailList) {
        return algorithmResultDetailList.stream()
                .anyMatch(dto -> !Constant.NEW_CREATE.equals(dto.getStatus()));
    }


    /**
     * 保存箱号信息
     * @param lpnList
     * @param algorithmResultDetailDTO
     */
    private void saveResultDetailLpnInfo(List<String> lpnList, AlgorithmResultDetailDTO algorithmResultDetailDTO) {
        List<AlgorithmResultDetailDTO> algorithmResultDetailDTOList = new ArrayList<>();
        for (String lpn : lpnList) {
            AlgorithmResultDetailDTO dto = new AlgorithmResultDetailDTO();
            BeanUtils.copyProperties(algorithmResultDetailDTO, dto);
            dto.setLpn(lpn);
            algorithmResultDetailDTOList.add(dto);
        }
        warehouseAlgorithmRepository.insertAlgorithmDetailList(algorithmResultDetailDTOList);
    }

}
