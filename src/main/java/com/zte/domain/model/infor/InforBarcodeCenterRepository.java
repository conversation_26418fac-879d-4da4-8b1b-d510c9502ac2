/***************************************************************************************** 
 * Copyright © 2003-2012 ZTEsoft Corporation. All rights reserved. Reproduction or       *
 * transmission in whole or in part, in any form or by any means, electronic, mechanical *
 * or otherwise, is prohibited without the prior written consent of the copyright owner. *
 ****************************************************************************************/
package com.zte.domain.model.infor;

import com.zte.interfaces.infor.dto.SkuDTO;
import com.zte.interfaces.infor.dto.TransferReelidRelationshipDTO;
import com.zte.interfaces.infor.vo.ItemBarcodeWmIdVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/** 
 * [描述] <br> 
 *  
 * <AUTHOR>
 * @version 1.0<br>
 * @taskId <br>
 * @CreateDate 2021年06月15日 <br>
 * @see com.zte.domain.model.infor <br>
 */
@Mapper
public interface InforBarcodeCenterRepository {
    /**
     * 获取条码库存
     * @param itemBarcode
     * @param wmwhseId
     * @return
     */
    Double getInventoryQty(@Param("itemBarcode") String itemBarcode, @Param("wmwhseId")String wmwhseId);

    /**
     *
     * @param itemBarcodes
     * @param wmwhseId
     * @return
     */
    List<ItemBarcodeWmIdVO> getInventoryQtyMap(@Param("itemBarcodes") List<String> itemBarcodes, @Param("wmwhseId")String wmwhseId);

    /**
     * 获取REELID转代码注册关系数据
     * @param dto
     * @return
     */
    List<TransferReelidRelationshipDTO> getTransferReelidRelationship(TransferReelidRelationshipDTO dto);

    /**
     * 更新REELID转代码注册关系数据
     * @param list
     */
    int updateTransferReelidRelationship(List<TransferReelidRelationshipDTO> list);

    /**
     * 获取物料名称
     * @param list
     * @return
     */
    List<SkuDTO> getSkuNameList(List<String> list);


    /**
     * 查询仓库
     * @return
     */
    List<String> getEnvWmwhids();

    /**
     * 查询试样超采的仓库
     * @return
     */
    List<String> getInventoryHoldWmwhids();

    /**
     * 查询不良品库存对应的仓库
     * @return
     */
    List<String> getBadInventoryHoldWmwhids();
}
