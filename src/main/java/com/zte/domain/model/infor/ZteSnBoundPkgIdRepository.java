package com.zte.domain.model.infor;

import com.zte.action.iscpedi.model.IscpEdiLog;
import com.zte.interfaces.infor.dto.UpdateZtePkgIdBoundSnInfoDTO;
import com.zte.interfaces.infor.dto.ZtePkgIdBoundSnInfoDTO;
import com.zte.interfaces.infor.dto.ZteSnBoundPkgIdDTO;
import com.zte.interfaces.step.dto.*;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface ZteSnBoundPkgIdRepository {

    /**
     * 查询SN绑定信息
     */
    List<ZteSnBoundPkgIdDTO> getSnBoundPkgId(ZteDeductionBillInfoDTO dto);
    List<IscpEdiLog> getAlibabaExternkey(ZteDeductionPlanParamDTO dto);

    List<ZtePkgIdBoundSnInfoDTO> queryZtePkgIdBoundSnInfoList(@Param("orderNo") String orderNo,@Param("pkgType") String pkgType);

    List<String> queryZtePkgIdBoundSnForExternalkey(@Param("pkgType") String pkgType);

    void updateZtePkgIdBoundSnInfo(UpdateZtePkgIdBoundSnInfoDTO dto);

    int updateZtePkgIdBoundSnInfoByResult(UpdateZtePkgIdBoundSnInfoDTO dto);

    /**
     * 查询所有原箱和混箱库存
     * @param inventoryWarehouseList
     * @return
     */
    List<ZteSnInventoryQueryDTO> getAllInventory(@Param("inventoryWarehouseList") List<ZteWarehouseInfoDTO> inventoryWarehouseList);

    /**
     * 查询指定外部单号的绑定关系详情
     * @return 绑定关系列表
     */
    List<ZtePkgIdBoundSnInfoDTO> queryPkgSnBindingsByExternalNo();

    String getWhseIdByBillNo(@Param("billNo") String billNo);
    int getOutQtyByBillNo(@Param("billNo") String billNo, @Param("whseId") String whseId, @Param("pickdetailkey") String pickdetailkey);

    /**
     * info出库推送存储中心与wms
     * issend -1 未发送， 提交成功，2 仓储中心异常， 3仓储中心成功\wms失败 4推送wms异常
     * @return
     */
    List<IscpEdiLog> getAlibabaSubmitStorageLog(ZteDeductionPlanParamDTO dto);
}