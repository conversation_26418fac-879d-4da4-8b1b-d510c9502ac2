package com.zte.domain.model.infor;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 待推送阿里记录实体类
 * 对应表：PENDING_PUSH_ALI_RECORD
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
@Accessors(chain = true)
@Builder(toBuilder = true)
public class PendingPushAliRecord {
    
    /**
     * 记录ID
     */
    private Long recordId;
    
    /**
     * 记录类型
     */
    private String recordType;
    
    /**
     * 交易key
     */
    private Long itrnSeriallKey;
    
    /**
     * 仓库号
     */
    private String whseId;
    
    /**
     * 消息ID
     */
    private String messageId;
    
    /**
     * 消息类型
     */
    private String messageType;
    
    /**
     * 发送状态
     */
    private String sendStatus;
    
    /**
     * 发送次数
     */
    private Integer sendTimes;
    
    /**
     * 创建时间
     */
    private Date addDate;
    
    /**
     * 创建人
     */
    private String addWho;
    
    /**
     * 最后更新时间
     */
    private Date editDate;
    
    /**
     * 最后更新人
     */
    private String editWho;
    
    /**
     * 是否有效
     */
    private String enabledFlag;


}

