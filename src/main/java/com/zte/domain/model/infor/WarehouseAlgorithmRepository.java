package com.zte.domain.model.infor;
import com.zte.interfaces.infor.dto.*;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface WarehouseAlgorithmRepository {

    int getWarehouseAlgorithmListVOTotal(WarehouseAlgorithmDTO dto);
    List<WarehouseAlgorithmDTO> getWarehouseAlgorithmList(WarehouseAlgorithmDTO dto);
    int saveWarehouseAlgorithm(WarehouseAlgorithmDTO dto);
    int updateWarehouseAlgorithm(WarehouseAlgorithmDTO dto);
    int saveWarehouseAlgorithmList(List<WarehouseAlgorithmDTO> list);
    int getWarehouseAlgorithmCount(@Param("list") List<WarehouseAlgorithmDTO> list);
    int getAlgorithmResultHeadListVOTotal(AlgorithmResultDetailDTO dto);
    List<AlgorithmResultHeadDTO> getAlgorithmResultHeadList(AlgorithmResultDetailDTO dto);
    int getAlgorithmResultDetailListVOTotal(AlgorithmResultDetailDTO dto);
    List<AlgorithmResultDetailDTO> getAlgorithmResultDetailList(AlgorithmResultDetailDTO dto);
    int getAlgorithmExecuteLogListVOTotal(AlgorithmExecuteLogDTO dto);
    List<AlgorithmExecuteLogDTO> getAlgorithmExecuteLogList(AlgorithmExecuteLogDTO dto);
    List<AlgorithmTaskDetailDTO> getAlgorithmTaskDetailList(AlgorithmResultDetailDTO dto);
    int getAlgorithmTaskDetailListTotal(AlgorithmResultDetailDTO dto);
    List<AlgorithmTaskDetailDTO> getAlgorithmTaskList(AlgorithmResultDetailDTO dto);
    int insertAlgorithmTaskDetailList(@Param("list") List<AlgorithmTaskDetailDTO> list);
    List<String> getInforInventoryInfo(AlgorithmResultDetailDTO algorithmResultDetailDTO);
    int deleteTaskDetailInfo(AlgorithmResultDetailDTO algorithmResultDetailDTO);
    int insertAlgorithmDetailList(List<AlgorithmResultDetailDTO> list);
}