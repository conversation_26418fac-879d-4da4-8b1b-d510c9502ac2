package com.zte.domain.model.infor;

import com.zte.interfaces.infor.dto.PendingPushAliRecordDTO;
import com.zte.interfaces.infor.dto.PendingQualityCheckTaskDTO;
import com.zte.interfaces.step.dto.ZteWarehouseInfoDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 待推送阿里记录数据访问接口
 */
@Mapper
public interface PendingPushAliRecordRepository {

    /**
     * 查询待质量判定库任务总数
     * @param zteWarehouseList
     * @return
     */
    int getPendingQualityCheckTaskListTotal(@Param("zteWarehouseList") List<ZteWarehouseInfoDTO> zteWarehouseList);

    /**
     *查询待质量判定库任务
     * @param dto
     * @return
     */
    List<PendingQualityCheckTaskDTO> getPendingQualityCheckTaskList(@Param("zteWarehouseList") List<ZteWarehouseInfoDTO> zteWarehouseList, @Param("dto") PendingQualityCheckTaskDTO dto);

    /**
     * 查询需要推送的数据
     *
     * @param zteWarehouseList
     * @param executeDate
     * @return
     */
    List<PendingPushAliRecord> selectSyncData(@Param("zteWarehouseList") List<ZteWarehouseInfoDTO> zteWarehouseList, @Param("executeDate") String executeDate);

    /**
     * 批量插入记录
     *
     * @param list
     * @return
     */
    int batchInsertPendingPushAliRecord(@Param("list") List<PendingPushAliRecord> list);

    /**
     * 查询待推送的记录
     *
     * @return
     */
    List<PendingPushAliRecord> selectWaitPushPendingRecord();

    /**
     * 批量更新状态
     *
     * @param recordIds
     * @param status
     * @param updateSendTimesFlag
     * @return
     */
    int batchUpdateSendStatus(@Param("recordIds") List<Long> recordIds, @Param("status") int status, @Param("updateSendTimesFlag") boolean updateSendTimesFlag);

    /**
     * 根据库房和记录ID查询推送时用到的数据
     *
     * @param whseIdList
     * @param recordIds
     * @return
     */
    List<PendingPushAliRecordDTO> selectPushAliData(@Param("whseIdList") List<String> whseIdList, @Param("recordIds") List<Long> recordIds);

    /**
     * 根据messageId更新状态
     *
     * @param messageId
     * @param status
     * @return
     */
    int updateSendStatus(@Param("messageId") String messageId, @Param("status") String status);
}

