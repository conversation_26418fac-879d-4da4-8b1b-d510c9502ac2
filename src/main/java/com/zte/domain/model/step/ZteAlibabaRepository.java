package com.zte.domain.model.step;
import com.zte.interfaces.infor.dto.AliOrderSubmitDetailDTO;
import com.zte.interfaces.step.dto.*;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface ZteAlibabaRepository {

    /**
     * 查询零星入库单、零星领料单、零星退库单制作(整机材料)、零星领料单制作(整机)
     */
    List<ZteDeductionBillInfoDTO> getBillInfo(ZteDeductionPlanParamDTO dto);

    /**
     * 更新单据头状态
     */
    int updateBillHead(ZteDeductionPlanParamDTO dto);

    /**
     * 根据客户计划id查询单据头信息
     */
    List<ZteDeductionBillInfoDTO> getBillHeadByPlanId(List<String> list);

    /**
     * 更新单据明细approvalQty
     */
    int updateBillDetailApprovalQty(List<ZteApproveResultDTO> list);

    /**
     * 更新单据明细openQty
     */
    int updateBillDetailOpenQty(List<ZteDeductionBillInfoDTO> list);

    /**
     * 查询未返回审批结果的明细数量
     */
    int getBillDetailNotApproved(ZteApproveResultDTO dto);

    /**
     * 记录阿里返回出入库审批结果
     */
    int insertZmsNoticeApproveResult(List<ZteApproveResultDTO> list);

    /**
     * 查询属于阿里管控的代码
     * @param itemNos 物料代码
     * @return
     */
    List<ItemNoMpnDTO> getControlItemNo(@Param("itemNos") List<String> itemNos);

    /**
     * 查询领料单公共方法
     *
     * @param zmsPicklistMainInDTO
     * @return
     */
    List<ZmsPicklistMainDTO> selectPicklistMain(ZmsPicklistMainInDTO zmsPicklistMainInDTO);

    /**
     * 阿里一码通管控，核销申请，查询核销原单据plan_id及detail_id
     */
    List<InforScatterSnDTO> getInforScatterSnDTOList(ZteDeductionBillInfoDTO dto);

    /**
     * 阿里非一码通管控，核销执行，查询核销数据
     */
    List<InforApprovalAlibabaDTO> getInforApprovalAlibabaList(@Param("billNo") String billNo);

    /**
     * 阿里非一码通管控，核销申请，查询可核销的出库单
     */
    List<InforEdiSoAlibabaDTO> getInforEdiSoAlibabaList(@Param("itemBarcode") String itemBarcode);

    /**
     * 阿里非一码通管控，核销申请，查询核销数据
     */
    int insertInforApprovalAlibaba(List<InforApprovalAlibabaDTO> list);

    int deleteInforApprovalAlibaba(@Param("billNo") String billNo);
    int updateInforApprovalAlibaba(List<ZteApproveResultDTO> list);
    int updateInforEdiSoAlibaba(List<InforEdiSoAlibabaDTO> list);
    List<StepSysLookupValuesDTO> getLookupValues(StepSysLookupValuesDTO sysLookupValuesDTO);

    List<AliOrderSubmitDetailDTO> getUnSplitBoxReceiveQty(@Param("boxNos")List<String> unsplitBoxNos);
}