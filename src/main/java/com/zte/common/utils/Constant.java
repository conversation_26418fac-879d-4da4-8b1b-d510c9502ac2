package com.zte.common.utils;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;

/**
 * 常量
 *
 * <AUTHOR>  2017年10月26日
 */
public class Constant {

    public static final String FLOWCODE = "flowCode";
    public static final String APPROVER= "approver";
    public static final String RESULT ="result";
    public static final String BUSINESSID ="businessId";
    public static final String APPROVAL_RESULT ="approvalResult";
    public static final String APPROVAL_COMPLETE = "COMPLETE";
    public static final String TASK_COMPLETED = "taskCompleted";
    public static final String TASK_STATUS = "status";
    public static final String NODE_NAME = "nodeName";
    public static final String APPROVAL_STQE = "STQE";

    public static final String SAVED ="SAVED";
    public static final String SUBMITED ="SUBMITED";
    public static final String SUBMITTED ="SUBMITTED";

    public static final String RECHECK ="RECHECK";
    public static final String CHECKING ="CHECKING";
    public static final String MOBILEAPPROVALKAFUKA="MOBILEAPPROVALKAFUKA";
    public static final String MOBILEAPPROVALSTART="ApprovalFlowStart";
    public static final String TESTING="TESTING";
    public static final String OMS_SCATTER_MACHINE_BOARD_IN ="OMS-SCATTER-MACHINE-BOARD-IN";
    public static final String OMS_SCATTER_MACHINE_MATERIAL_IN="OMS-SCATTER-MACHINE-MATERIAL-IN";
    public static final String OMS_SCATTER_MACHINE_OUT="OMS-SCATTER-MACHINE-OUT";
    public static final String IWMS_INVENTORY_HOLD="IWMS-INVENTORY-HOLD";
    public static final String IWMS_INVENTORY_REMOVE_HOLD_PLAN="OMS-REMOVE-HOLD-PLAN";
    public static final String IWMS_INVENTORY_REMOVE_HOLD_QUALITY="OMS-REMOVE-HOLD-QUALITY";
    public static final String LIST = "list";
    public static final String TOATL = "total";
    public static final String FROM = "from";
    public static final String EDI_PO_S = "EDI_PO_S";
    public static final String EDI_SO_S = "EDI_SO_S";
    public static final String SUCESS_CODE = "0000";
    // 单板单据类型
    public static final String VENEER = "20";
    /**
     * 状态码404
     * 页面未找到
     */
    public static final String FAILED = "FAILED";
    public static final String DEALED = "DEALED";
    public static final String DEALING = "DEALING";
    public static final String BILL_NO_SEQUENCE = "100001";
    public static final String BILL_ID_SEQUENCE = "100002";
    public static final String DETAIL_ID_SEQUENCE = "100003";
    public static String TASK_NUM_CODE_LOT = "1543130";
    public static final String EMAIL_RECEIPTS="1543125";
    public static final String SPLIT_22=";";
    public static final String ISCP_WARN = "ISCP出入库反馈告警";
    public static final String INFOR_TEXT = "infor:";
    public static final String ZTE_EMAIL_SUFIIX = "@zte.com.cn";
    public static final String STEP_TEXT = "step:";
    public static final String PIECE_TEXT = "条;";
    public static final String ISCP_KAFKA_TEXT = "iscpKafka消费失败:";
    public static final String ISCP_WARN_CONTENT = "出入库反馈异常数据数量:";
    public static final String WEB_CHART_ADD = "application/soap+xml; charset=utf-8";
    public static final String STR_AGREE = "同意";
    public static final String STR_SCRAP = "报废";
    public static final String STR_BOX = "箱";
    public static final String STR_BAG = "袋";
    /**
     * 系统类型
     */
    public static final String LIST_TYPE_MES = "mes+";
    public static final String TRANSFER_OWNER_CLASS = "WMS_M07_TransferOwnerClassInfoSrv";
    public static final String FLAG_Y = "Y";
    public static final String FLAG_N = "N";
    public static final String FLAG_NO = "NO";

    public static final String DATE_FORMATER_YYYYMMDD = "yyyy-MM-dd";
    public static final String YYYY_MM_DD_HH_MM_SS = "yyyy-MM-dd HH:mm:ss";
    public static final String START_TIME = "05:00:00";
    public static final String END_TIME = "07:00:00";
    public static final String LOCAL_RESOLVER = "localeResolverMes";

    public static final String[] PDF_HEADS = new String[]{"销售订单", "销售领料单", "发货仓库", "SO单号", "物料代码"
            , "物料条码", "落放ID", "拟领数量", "发货数量", "班组", "用途", "物料名称", "领料人", "领料单位"};

    public static final String[] EXCEL_TITLE_STB_DELIVERY_INFO_QUERY = new String[]{
            "合同号", "发货指令号(DO单)", "任务发货日期", "装箱单号", "物料条码(产品SN)",
            "物料代码", "物料名称", "生产单位", "生产日期", "品牌",
            "设备种类", "型号", "生产任务号(外协)", "制造工艺单号", "MAC1",
            "MAC2", "单板SN1", "单板SN2", "产品SN", "纸箱SN",
            "栈板SN", "电源SN", "遥控器SN", "FT测试结果", "FT测试时间",
            "老化测试结果", "老化测试时长", "MAC配置结果", "MAC配置时间", "开关机测试结果",
            "开关机测试时间", "整机测试结果", "整机测试时间", "整机校验结果", "征集校验时间",
            "出厂配置结果", "出厂配置时间", "软件版本号", "Logo版本号", "出厂配置文件名",
            "备注1", "备注2", "备注3", "备注4", "备注5",
            "备注6", "备注7", "导入人", "导入时间", "最后更新时间"};

    public static final String[] EXCEL_PROPS_STB_DELIVERY_INFO_QUERY = new String[]{
            "mainContractNo", "frInsNo", "issueDate", "billNumber", "barcode",
            "itemNo", "itemName", "productionUnit", "productionDate", "brand",
            "deviceType", "model", "productionEntityno", "produceBillno", "mac1Addr",
            "mac2Addr", "boardSn1", "boardSn2", "productSn", "cartonSn",
            "palletSn", "powersourceSn", "remotecontrolSn", "testResult", "testTime",
            "ageingTestresult", "ageingTesttime", "macConfigresult", "macConfigtime", "onoffTestresult",
            "onoffTesttime", "machineTestresult", "machineTesttime", "machineCheckresult", "machineChecktime",
            "facConfigresult", "facConfigtime", "softVerNum", "logoVerNum", "facConfigfile",
            "remark1", "remark2", "remark3", "remark4", "remark5",
            "remark6", "remark7", "createdFullBy", "creationDate", "lastUpdateDate"};

    public static final String PDF_TITLE = "康讯发货清单";

    public static final String CFG_RULE_ITEM_TYPE_FIXEDVALUE = "302000300001";

    public static final String CFG_RULE_ITEM_TYPE_SEQUENCE = "302000300002";

    public static final String CFG_RULE_ITEM_TYPE_DATE = "302000300003";

    public static final String MFG_SITE_TYPE = "主设备";

    public static final long REDIS_MONTH_EXPIRE = 60 * 60 * 24 * 31;
    public static final long REDIS_YEAR_EXPIRE = 60 * 60 * 24 * 31 * 12;

    public static final String ESCAPE_QUOTE = "\"";
    //批量操作每次的数量
    public static final int SPLITSIZE_TEN = 10;
    public static final int SPLITSIZE_FIFTY = 50;
    public static final int SPLITSIZE_HUNDRED = 100;
    public static final int BOM_LENGTH = 12;
    public static final int SPLITSIZE_FIVE_HUNDRED = 500;

    public static final String COMMA = ",";
    public static final String SINGLE_QUOTE = "'";
    public static final String JSON_BO = "bo";
    public static final String JSON_CODE = "code";
    public static final String JSON_MSG = "msg";
    public static final String WORK_ORDER_SOURCE_STEP = "STEP";
    public static final String WORK_ORDER_SOURCE_WMES = "WMES";
    public static final long NUMBER_ZREO = 0;

    public static final String X_FACTORY_ID = "X-Factory-Id";
    public static final int NUMBER_2 = 2;
    public static final int NUMBER_26 = 26;
    public static final int NUMBER_16 = 16;
    public static final String CENTER_LINE = "-";
    public static final String TYPE_SEQUENCE_CODE = "序列码";
    public static final String TYPE_BATCH_CODE = "批次码";
    public static final String HORIZON = "-";
    public static final String STRING_EMPTY = "";
    public static final String DATA = "data";
    public static final String STR_NUMBER_ONE = "1";
    public static final String STR_NUMBER_TWO = "2";
    public static final String STR_NUMBER_ZERO = "0";
    public static final String STR_NUMBER_THREE = "3";
    public static final String STR_NUMBER_FIVE = "5";

    public static final String POINT = ".";

    public static final int INT_M_1 = -1;
    public static final String ORG_ID_0000 = "0000";
    public static final String IWMS_DATAWB = "IWMS-DATAWB";
    public static final String DEAL_LOTTABLES = "DealLottablesDetail";
    public static final String D05 = "05";
    public static final String SYS = "SYS";
    public static final String LOT = "LOT";
    public static final String LOG = "LOG";
    public static final String TO = "TO";
    public static final int INT_0 = 0;
    public static final int INT_1 = 1;
    public static final int INT_2 = 2;
    public static final int INT_3 = 3;
    public static final int INT_12 = 12;
    public static final int INT_31 = 31;
    public static String POOL = "pool-io-";
    public static String TASK_NUM_CODE = "1543112";
    public static String SYNC_ECCN_LOOKUP_TYPE = "1543129";
    public static String SYNC_ECCN_LOOKUP_CODE = "1543129000001";
    public static String ENV_WMWHSES = "1543114";
    public static String WMIDS = "WMIDS";
    public static String WMWHSE = "wmwhse";
    public static String THREAD = "-thread";
    public static String MRP_DICS = "1000018";
    public static String MRP = "mrp: ";
    public static String UNKNOWN = "UNKNOWN";
    public static String RUN_ERROR = " run error";
    public static String NJLTC = "NJLTC";
    public static final int INT_B1 = -1;
    public static final String STR_B1 = "-1";
    public static final int INT_100 = 100 ;
    public static final int INT_200 = 200 ;
    public static final int INT_6 = 6;
    public static final int INT_7 = 7;
    public static final int INT_3000 = 3000;
    public static final int INT_4000 = 4000;
    public static final int INT_5000 = 5000;
    public static final int INT_500 = 500;
    public static final int INT_2999 = 2999;
    public static final int INT_1000 = 1000;
    public static final int INT_20000 = 20000;
    public static final int INT_20001 = 20001;
    public static final String ONE_SPLIT = "-";
    /**
     * 精确到秒，格式：yyyy-MM-dd HH:mm:ss
     */
    public static String DATE_FORMATE_FULL = "yyyy-MM-dd HH:mm:ss";
    public static final String BLANK = "";
    public static final String PLACE = "%07d";
    public static final String SPLIT = ",";
    public static final String STATUS = "STATUS";

    public static final String[] PKG_BATCH_SCAN_VALID_FIELD = new String[]{
            "boxNo", "taskNo", "itemName", "itemType", "itemCode"};

    public static final String ORG_ID = "orgId";
    public static final String X_ENTITY_ID = "xEntityId";
    public static final String X_BILL_ID = "xBillId";
    public static final String X_ITEM_ID = "xItemId";
    public static final String X_EXIT = "xExit";
    public static final String X_REL_EXIT = "xRelExit";
    public static final String X_ISREPEAT = "xIsrepeat";
    public static final String START_SN = "START_SN";
    public static final String END_SN = "END_SN";
    public static final String CNT = "CNT";
    public static final String P_MESSAGE = "pMessage";

    public static final String GET_LPN_ERROR = "获取栈板信息失败";

    public static final String X_SUBMACHINE_NUMBER = "xSubmachineNumber";
    public static final String X_BOX_NO = "xBoxNo";
    public static final String X_ITEM_TYPE = "xItemType";
    public static final String X_NUM = "xNum";
    public static final String X_ITEM_BARCODE = "xItemBarcode";
    public static final String X_ITEM_CODE = "xItemCode";
    public static final String X_ITEM_NAME = "xItemName";
    public static final String X_ITEM_UNIT = "xItemUnit";
    public static final String X_CREATE_BY = "xCreatedBy";

    public static final String BOX_NO_NOT_EXIST = "输入的箱号不存在，请确认后重新输入！";
    public static final String BOX_NO_NOT_IN_TASK = "输入的箱号不属于当前的任务号，请确认后重新输入！";
    public static final String BOX_NO_DUPLITE = "该箱号已经扫描并录入数据，请确认后重新输入！";
    public static final String LPN_NUM_MORETHAN_CAN_SCANED = "栈板号的数量超出了可扫描数量！";

    public static final String NULL = "null";

    public static final String SYSTEM_BOARDS = "系统单板";
    public static final String POWER_BOARD = "电源单板";
    public static final String PERIODIC_CALCULATION_FAILURE = "定时系统单板周期计算单板失败 --> BSN:";
    public static final String INSERT_PRODPKANID_FAILURE = "定时系统单板周期重复插入批次数据到数据库 --> PRODPLAN_ID:";
    public static final String UPDATE_PRODLANID_FAILUERE = "定时系统单板周期更新批次数据到数据库失败 --> PRODPLAN_ID:";
    public static final String UPDATE_BOM_FAILE = "定时系统单板周期更新BOM数据到数据库失败 --> BOM_NO:";
    public static final String CALCULATING_THE_PERIODIC_PREDICTION_OUTPUT = "部件预测产出周期计算任务";
    public static final String INSERT_BAORD_PERIODIC = "定时系统单板周期重复插入单板周期计算记录到数据库 --> calcStartDate:";
    public static final String PERIOD_CALCULATING_THREAD = "周期计算线程";
    public static final String DATA_MERGING_THREAD = "数据合并线程";
    public static final String BOARD_ONLINEOLD = "board_onlineold";
    public static final String SCAN_BOARDONLINE = "scan_boardonline";
    public static final String SYNCECCNSTOCKDATA = "syncEccnStockData异常:";
    public static final String INTTSYNCECCNSTONCKDATA = "initSyncEccnStockData异常:";
    public static final String SETINITPARAMS = "setInitParams异常:";
    public static final String SYNCECCNSTOCKDATACONDITOIN = "syncEccnStockDataCondition异常:";
    public static final String SETPARAMSCONDITION = "setParamsCondition异常:";
    public static final String STBEXCELBIGDATAEXPORT = "StbExcelBigDataExport_";
    public static final String INPUTQTY = "InputQty";
    public static final String TASKQTY = "TaskQty";
    public static final String PACKTASKQTY = "PackTaskQty";
    public static final String THREEMONTH = "ThreeMonth";
    public static final String LASTYEATMONTH = "LastYearMonth";
    public static final String WMESPRODUCTIONINFOBOARDLASTYEAROF = "WmesProductionInfoBoardLastYearOf";
    public static final String BEFORETHREEMONTH = "BeforeThreeMonth";
    public static final String WMESPRODUCTION = "WmesProductionInfoBoardThisYearOfEarlyMonths";
    public static final String PRODUCTCATEGORYNAME = "productCategoryName";
    public static final String NONE = "无";
    public static final String STR_YES = "是";
    public static final String STR_FALSE = "false";
    public static final String STR_TRUE = "true";
    public static final String ECSS_ITEM_ADDING = "ECSSMaterialAdding";
    public static final String ECSS_ITEM_ADDED = "ECSSMaterialAdded";
    public static final String ISCP = "ISCP";
    public static final String SYSTEM = "SYSTEM";
    public static final String INFOR = "INFOR";
    public static final String SC = "SC";
    public static final String NO_DATA = "没有数据";
    public static final String EXCUTE_SUCCESS = "执行成功";
    public static final String OPERATE_SUCCESS = "操作成功";
    public static final String BARCODE = "barcode";
    public static final String PARENT_CATEGORY_CODE = "parentCategoryCode";
    public static final String ITEM_CODE = "itemCode";
    public static final String ITEM_NAME = "itemName";
    public static final String SOURCE_SYSTEM = "sourceSystem";
    public static final String SOURCE_BATCH_NO = "sourceBatchNo";
    public static final String REMARK = "remark";
    public static final String REEL_MP_CODE = "REEL_MP_CODE";
    public static final String INFORWMS = "INFORWMS";
    public static final String SN_CODE = "SN_CODE";
    public static final String X_TENANT_ID = "X-Tenant-Id";
    public static final String X_EMP_NO = "X-Emp-No";
    public static final String ROWS = "rows";
    public static final String SUSSESS = "0000";
    public static final String PAGE = "page";
    public static final String TASKNO = "taskNo";
    public static final String X_AUTH_VALUE = "X-Auth-Value";
    public static final String YYMMDDHHMMSS = "yyyy-MM-dd HH:mm:ss";
    public static final String ZTE_ISS_BARCODECENTER_BARCODE = "zte-iss-barcodecenter-barcode";
    public static final String ZTE_ISS_BARCODECENTER_BARCODE_ENTRY = "/barcode/invalidReelOrSN";
    public static final String ZTE_ISS_STOCK_CENTER = "zte-iss-stock-center";
    public static final String ZTE_MES_MANUFACTURESHARE_CENTERFACTORY = "/zte-mes-manufactureshare-centerfactory";
    public static final String ZTE_MES_RESOURCEWAREHOUSE_BILL = "/zte-mes-resourcewarehouse-bill";
    public static final String ZTE_ISS_STOCK_CENTER_ENTRY = "/req-head/submitReqHeadAndBatchDetails";
    public static final String SPECIFIED_PS_TASKLIST = "/PS/getSpecifiedPsTaskList";
    public static final String X_SOURCE_SYS = "X-Source-Sys";
    public static final String STR_R_WMS = "rWMS";
    public static final String ZTE_ISS_BARCODECENTER_BARCODE_UPDATE = "/barcode/update";
    public static final String ZTE_SCM_WMS_SSTOCK = "zte-scm-wms-sstock";
    public static final String ZTE_SCM_WMS_SSTOCK_INBOUND = "/inbound/saveAndCommitInboundReq";
    public static final String ZTE_SCM_WMS_SEND_EXTERNAL_DETAIL = "/externalOrder/receiveRequisitionDetail";
    public static final String ZTE_SCM_WMS_SSTOCK_SUBMIT_REQ = "/reqHead/submitReqAndTask";
    public static final String ZTE_SCM_WMS_SSTOCK_QUERY_INVENTORY = "/StockController/queryInventoryByType";
    public static final String OLD_SKU_ZH = "原物料代码";
    public static final String OLD_LOTTABLE02_ZH = "原220条码";
    public static final String OLD_SERIALNUMBER_ZH = "原REELID";
    public static final String LAN_ZH = "zh";
    public static final String LAN_EN = "en";
    public static final String ZTE_CORP = "ZTE_Corp";
    public static final String ZTE_ECSS_BATCHMATERIAL_ASYNC = "/zte-grc-ecss-feedersystemapi/pub-api/v2/batchmaterial/async";
    public static final String SHIPPING_TYPE_LEVEL_ONE = "shipping_type_level_one";
    public static final String R = "R";
    public static final String E = "E";
    public static final String S = "S";
    public static final String NO_OVER_BARCODE="输入条码数量不能超过200个，请重新输入";
    public static final String PARAM_MUST_INPUT="输入参数必须都输入，请重新输入";
    public static String JOB_AUTO_TRANSFER_BILL = "JOB_AUTO_TRANSFER_BILL";
    public static final String TRANSFER_SOURCESYSTEMID = "100000150843";
    public static final String TRANSFER_SOURCESYSTEMNAME = "ZXSCM WMS100";
    public static final String TRANSFER_WEBSERVICE_CODE = "WMS_M07_TransInboundInfoSrv";
    public static final String TRANSFER_SOURCESYS_USER = "10038856";
    public static final String TRANSFER_MAKE_ERROR = "调拨单定时任务失败";
    public static final String TRANSFER_SUCCESS = "SUCCESSFUL";
    public static final String TRANSFER_FAILED = "FAILED";
    public static final String TRANSFER_EMAIL_USER = "1000019";
    public static final String TRANSFER_FAILED_BILL="调拨单自动提交infor失败单据如下:";
    public static final String TRANSFER_FAILED_WARN="调拨单自动提交infor单据提醒";
    public static final String TRANSFER_AUTO_BILL="调拨单自动提交infor单据如下:";
    public static final String TRANSFER_SUCCESS_BILL="调拨单自动提交infor成功单据如下:";
    public static final String STING_C = "C";
    public static final String BILL_NAME_C = "销售领料单";
    public static final String BILL_TYPE_CODE_C = "DT-KX-S-010";
    public static final String STING_ND = "ND";
    public static final String BILL_NAME_ND = "备料售卖领料单";
    public static final String BILL_TYPE_CODE_ND = "DT-KX-S-020";
    public static final String COUNTRY_DIGIT_CODE = "CN";
    public static final String BUSINESS_PARTNER_FUNCTION_CODE = "BPF004";
    public static final String BUSINESS_PARTNER_TYPE_CODE = "BPT002_Customer";
    public static final String X_LANG_ID = "X-Lang-Id";
    public static final String X_LANG_ID_ZH = "zh";
    public static final String CONTENT_TYPE = "Content-Type";
    public static final String APPLICATION_JSON = "application/json";
    public static final String X_FEEDER_SYSTEM_TOKEN = "X-FeederSystem-Token";
    public static final String ECSS_MATERIAL_ADDING = "ECSSMaterialAdding";
    public static final String ECSS_MATERIAL_ADDED = "ECSSMaterialAdded";
    public static final String ECSS_BILL_ADDING = "ECSSBillAdding";
    public static final String ECSS_BILL_ADDED = "ECSSBillAdded";
    public static final String GTSAUDITING = "GTSAUDITING";
    public static final String ECSSAUDITING = "ECSSAUDITING";
    public static final String ECSSAUDITED = "ECSSAUDITED";
    public static final String AUDITING = "AUDITING";
    public static final String RELEASED_ZH = "通过";
    public static final String NOT_RELEASED_ZH = "不通过";
    public static final String LOOKUP_CODE_100002000001 = "100002000001";
    public static final String ECSS_BILL_ASYNC = "/zte-grc-ecss-feedersystemapi/pub-api/v2/document/async";
    public static final List<String> ECSS_RELEASED_STATUS = Arrays.asList("RELEASED", "RELEASED_AUTO_LICENCE_USED", "RELEASED_MANUAL_LICENCE_USED");
    public static final String LOOKUP_TYPE_1000022 = "1000022";
    public static final String ECSS_BILL_FAILED = "ECSS单据同步失败超4次告警";
    public static final String ECSS_ITEM_ASYNC_FAILED_WARN="OMS系统售卖领料单调用ECSS新增物料异步接口失败超4次提醒";
    public static final String ECSS_SALE_BILL_FAILED_WARN="OMS系统售卖领料单调用ECSS新增单据异步接口失败超4次提醒";
    public static final String ECSS_SALE_BILL_FAILED_1="OMS系统售卖领料单";
    public static final String ECSS_SALE_BILL_FAILED_2="调用ECSS新增单据异步接口失败超4次，请及时处理";
    public static final String ECSS_ITEM_ASYNC_FAILED="调用ECSS新增物料接口失败，请及时处理";
    public static final String ECCN_INDFUNC_EMPTY="该单据下ECCN接口获取物料是否有独立功能部分为空！";
    public static final String STRING_LTH="LTH";
    public static final String BILL_NAME_LTH = "来料退货申请单";
    public static final String BILL_TYPE_CODE_LTH = "DT-SC-070-1";
    public static final String STRING_STH="STH";
    public static final String BILL_NAME_STH = "在线退货申请单";
    public static final String BILL_TYPE_CODE_STH = "DT-SC-070-2";
    public static final String BUSINESS_PARTNER_FUNCTION_CODE_BPF001 = "BPF001";
    public static final String BUSINESS_PARTNER_TYPE_CODE_BPT001_SUPPLIER = "BPT001_Supplier";
    public static final String ECSS_BILL_TERMINATE = "/zte-grc-ecss-feedersystemapi/pub-api/v2/document/terminate";
    public static final String LOOKUP_TYPE_1000039 = "1000039";
    public static final String RETURN_APPLY_SUBMIT_TO_INFOR_URL ="/material/submitApllyToInforSo";
    public static final String RETURN_APPLY_NO = "returnapply_no";
    public static final String ECSS_RETURN_BILL_FAILED_WARN="IWMS系统退货申请单调用ECSS新增单据异步接口失败超4次提醒";
    public static final String IWMS_RETURN_BILL="IWMS系统退货申请单";
    public static final String IWMS_RETURN_BILL_SUBMIT_TO_INFOR_FAILED_WARN="IWMS系统退货申请单推送INFOR失败超4次提醒";
    public static final String IWMS_RETURN_BILL_SUBMIT_TO_INFOR_FAILED="推送INFOR失败超4次，请及时处理";
    public static final String UNLOCK_URL="/delayCheck/inforStockHold";
    public static final String SAMPLE_HOLD= "SAMPLEHOLD";
    public static final String INVENTORY_HOLD_NAME= "冻结库存记录清单";
    public static final String INVENTORY_HOLD_EXPORT= "冻结库存记录导出";
    public static final String SHEET1= "sheet1";
    public static final String LOOKUP_TYPE_1000037= "1000037";
    public static final String STR_QUALITY_HOLD= "质量冻结";
    public static final String STR_PLAN_HOLD= "计划冻结";
    public static final String STR_SAMPLE_HOLD= "试样冻结";
    public static final String LOOKUP_TYPE_1000038= "1000038";
    public static final String IWMS_QUALITY_HOLD_HEAD= "iWMS材料质量隔离通知";
    public static final String IWMS_QUALITY_HOLD_NAME= "iWMS材料质量隔离物料清单";
    public static final String IWMS_DB_QUALITY_HOLD_HEAD= "iWMS单板质量隔离通知";
    public static final String IWMS_DB_QUALITY_HOLD_NAME= "iWMS单板质量隔离物料清单";
    public static final String IWMS_INVENTORY_HOLD_FAILED_HEAD= "iWMS非质量隔离失败超4次提醒";
    public static final String IWMS_INVENTORY_HOLD_FAILED_NAME= "iWMS非质量隔离失败超4次物料清单";
    public static final String IWMS_DB_INVENTORY_HOLD_FAILED_HEAD= "iWMS单板质量隔离失败超4次提醒";
    public static final String IWMS_DB_INVENTORY_HOLD_FAILED_NAME= "iWMS单板质量隔离失败超4次物料清单";
    public static final String IWMS_CL_INVENTORY_HOLD_FAILED_HEAD= "iWMS材料质量隔离失败超4次提醒";
    public static final String IWMS_CL_INVENTORY_HOLD_FAILED_NAME= "iWMS材料质量隔离失败超4次物料清单";
    public static final String LOOKUP_TYPE_1000040= "1000040";
    public static final String INFOR_ALLCATE_EXCEPTION_MONITOR= "反先进先出异常监控";
    public static final String INFOR_FEFO_EXCEPTION_MONITOR= "反到期先出异常监控";
    public static final String INFOR_ALLCATE_EXCEPTION_CONTANT= "未发现异常";
    public static final String INFOR_ALLCATE_EXCEPTION_MONITOR_NAME= "反先进先出物料清单";
    public static final String INFOR_FEFO_EXCEPTION_MONITOR_NAME= "反到期先出物料清单";
    public static final String GENERATE_QUERY_RESULT_EXCEL_FAILED="反先进先出预警监控失败";
    public static final String FEFO_QUERY_RESULT_EXCEL_FAILED="反到期先出预警监控失败";
    public static final String CLOUD_DISK= "cloudDiskHelper";
    public static final String INFOR_ALLCATE_EXCEPTION_MONITOR_DOWN= "excel文档已就绪，请前往下载：";
    public static final String STR_CLOSED= "CLOSED";
    public static final String STR_EXCCLOSED= "EXCCLOSED";
    public static final String STR_95 = "95";
    public static final String STR_COMPLETE = "完成";
    public static final String STR_COMPLETED = "已完成";
    public static final String STR_TOSTART = "未开始";
    public static final String STR_OUTBOUND = "发料中";
    public static final String STR_SHORTBOUND = "缺料中";
    public static final String LOOKUP_TYPE_1000020= "1000020";
    public static final String STR_NUMBER_FOUR = "4";
    public static final String LOOKUP_TYPE_1000029 = "1000029";
    public static final String LEAK_SCAN_GTS_ECSS = "【一级告警-售卖单漏GTS/ECSS扫描】";
    public static final String LEAK_SCAN_GTS_ECSS_FAILED = "【一级告警-售卖单漏GTS/ECSS扫描监控失败】";
    public static final String LEAK_SCAN_GTS_ECSS_CONTENT = "漏扫描单据如下：";
    public static final String  STR_FEED = "\r\n";
    public static final String UTC = "UTC";
    public static final String WEBSERVICE_LOOKUP_TYPE= "1000041,1000042";
    public static final String LOOKUP_TYPE_1000041= "1000041";
    public static final String LOOKUP_TYPE_1000042= "1000042";
    public static final String WEBSERVICE_MONITORING="WebService查询接口超时监控";
    public static final String INTERFACE_TIME="接口用时";
    public static final String TIMED_OUT="秒，已超时";
    public static final String INVENTORY_HOLD_EX_NAME= "冻结库存异常记录清单";
    public static final String INVENTORY_HOLD_EX_EXPORT= "冻结库存异常记录导出";
    public static final String  STR_BLANK = "\t\n";
    public static final String LOOKUP_TYPE_1000045= "1000045";
    public static final String QUALITY_HOLD_LOOKUP_TYPE= "1000038,1000045";
    public static final String OVERDUE_MATERIAL_NAME = "超期发料需求清单";
    public static final String OVERDUE_MATERIAL_EXPORT = "超期发料需求导出";
    public static final String LOOKUP_TYPE_1000044= "1000044";
    public static final String INFOR_OVERDUE_MATERIAL_MONITOR= "三次超期复检信息";
    public static final String INFOR_OVERDUE_MATERIAL_CONTANT= "未发现三次超期物料";
    public static final String INFOR_OVERDUE_MATERIAL_MONITOR_NAME= "三次超期复检物料清单";
    public static final String INFOR_OVERDUE_MATERIAL_FAILED="三次超期复检信息预警监控失败";
    public static final String INONE_APPCODE = "APPCode";
    public static final String PLAN_INONE_URL = "/ZXIAPS/iAPS300/zte-aps-scpa-prodplan/prodPlan/getKitItemList";
    public static final String ITEM_LIST_PLAN = "itemNoList";
    public static final String TWO_SPLIT = "_";
    public static final String INFOR_WMS = "InforWMS";
    public static final String ZTE1 = "ZTE1";
    public static final String BYTE_DANCE = "ByteDance";
    public static final String BAIDU = "Baidu";
    public static final String ZTE_IMES_BYTEDANCE_INVENTORY = "ZTEiMES-ByteDance-Inventory";
    public static final String ZTEIMES_BAIDU_STOCK = "ZTEiMES-Baidu-Stock";
    public static final String ZTE_IMES_BYTEDANCE_INVENTORY_MOVEMENT = "ZTEiMES-ByteDance-InventoryMovement";
    public static final String PUSH_DATA_TO_B2B = "/customerDataLog/pushDataToB2B";
    public static final String GET_CUSTOMER_ITEMS_INFO_URL = "/customerItemsCtrl/getCustomerItemsInfo";
    public static final String PUSH_DATA_TO_B2B_KAFKA = "/tradeDataLog/pushData";
    public static final String BULK_QUERIES_INONE_URL = "/psTaskExtended/bulkQueriesByTaskNos";
    public static final String GET_CUSTOMER_ITEMS_BY_CUSTOMER_AND_MODE_URL = "/customerItemsCtrl/getCustomerItemsByCustomerAndMode";
    public static final String ZTE_IMES_MEITUAN_INVENTORY = "ZTEiMES-Meituan-Inventory";
    public static final String X_MES_BFF = "X-Mes-Bff";
    public static final String IMES_BFF_AUTHORIZATION_DEFAULT  = "iMes-bff-authorization";
    public static final String QUERY_FIXBOM_DETAIL_URL = "/fixBomCommonController/queryFixBomDetailByFixBomId?fixBomId=";
    public static final String INFOR_ADDSNBINDDATA_URL = "/infor/addSnBindData";
    public static final String STOCK_INFO_UPLOAD_MONITOR = "【一级告警-库存数据上传失败告警】";
    public static final String STOCK_INFO_UPLOAD_FAILED = "库存数据上传失败：";
    public static final String STOCK_MOVE_INFO_UPLOAD_FAILED = "库存移动数据上传失败：";
    public static final String LOOKUP_TYPE_1000032 = "1000032";
    public static final String H = "H";
    public static final String T = "T";
    public static final String F = "F";
    public static final String DELI_NO_LIST = "deliNoList";
    public static final String PAGE_NO = "pageNo";
    public static final String PAGE_SIZE = "pageSize";
    public static final String SEARCH_MAP = "searchMap";
    public static final String REQUEST_TYPE = "requestType";
    public static final String ORDER_QUERY_FOR_INFOR = "orderQueryForInfor";
    public static final String IS_LAST_PAGE = "isLastPage";
    public static final String BILL_NO_LIST = "billNoList";
    public static final String RETURN_ORDER_QUERY_FOR_INFOR = "returnOrderQueryForInfor";
    public static final String ISRM_ENTRY_INONE_URL = "/ZXISS_iSRM300/UI/zte-scm-iscp-bff-service/bff/entry";
    public static final String ERP_ITEM_INONE_URL = "/ZXIFOL_ERP300/OERP/publicQueryData/v01";
    public static final String EXTERNAL_ITEM_INONE_URL = "/ZXISS_iSRM300/UI/zte-scm-iscp-bff-service/external/item";
    public static final String BARCODE_CENTER_INONE_URL = "/ZXISS/iBarcode300/zte-iss-barcodecenter-barcode/rule/ruleParseAndRegister";
    public static final String ZTE_IMES_BYTEDANCE_GOODS_RECEIPT_OF_PURCHASE_ORDER = "ZTEiMES-ByteDance-GoodsReceiptOfPurchaseOrder";
    public static final String RECEIPT_AND_PURCHASE_ORDER_INFO_UPLOAD_FAILED = "采购订单收货数据上传失败：";
    public static final String YYYY_MM_DD_HH_MM_SS_SSS = "yyyyMMddHHmmssSSS";
    public static final String STOCK_INFO_ZH = "库存";
    public static final String OMS_REQUISITION_BILL_ZH = "OMS零星领料工单发料计划";
    public static final String OMS_RETURN_BILL_ZH = "OMS零星退料工单发料计划";
    public static final String STR_STORE = "入库";
    public static final String STR_STORE_EN = "DP";
    public static final String STR_OUT_STORE = "出库";
    public static final String STOCK_MOVE_INFO_ZH = "库存移动";
    public static final String PURCHASE_ORDER_ZH = "采购订单收货";
    public static final String HOLD_APPROVE = "hold_approve";
    public static final String HOLD_ALL = "全部";
    public static final String HOLD_TITLE = "iWMS库存隔离条码%s";
    public static final String HOLD_SPLIT = "/";
    public static final String INVENTORY_HOLD = "冻结";
    public static final String INVENTORY_REMOVE_HOLD = "解除冻结";
    public static final String REMOVE_HOLD_TITLE = "iWMS库存解除隔离条码%s";
    public static final String APPROVE_TITLE = "title";
    public static final String APPROVE_PSB= "配送部";
    /**
     * GTS对接
     */
    public static final String STR_YYYY_MM_DD_HH_MM_SS = "yyyyMMddHHmmss";
    public static final String STR_OMS = "OMS";
    public static final String STR_CNKX = "CNKX";
    public static final String STR_ZDO = "ZDO";
    public static final String STR_AG = "AG";
    public static final String STR_WE = "WE";
    public static final String STR_KX01 = "KX01";
    public static final String STR_TAN = "TAN";
    public static final String STR_PCE = "PCE";
    public static final String STR_CNY = "CNY";
    public static final String STR_A = "A";
    public static final String GTS_BILL_ADDING = "GTSBillAdding";
    public static final String GTS_BILL_ADDED = "GTSBillAdded";
    public static final String GTS_BILL_FAILED = "GTS单据同步失败超4次告警";
    public static final String GTS_SALE_BILL_FAILED_2 = "调用GTS新增单据异步接口失败超4次，请及时处理";
    public static final String GTS_SALE_BILL_FAILED_WARN = "OMS系统售卖领料单调用GTS新增单据异步接口失败超4次提醒";
    public static final String ERP_INONE_PUBLIC_QUERY_DATA = "/ZXIFOL_ERP300/OERP/publicQueryData/v01";
    public static final String ERP_API_CODE_GET_ITEM_COST = "getitemCost";
    public static final String ERP_PRODUCT_TYPE_EPMS = "EPMS";
    public static final String ERP_SERVICE_CODE_EPMS_MANAGE = "zte-epms-manage";
    public static final String STR_ORGANIZATION_ID = "organization_id";
    public static final String LOOKUP_CODE_100003300001 = "100003300001";
    public  static  final String X_TIMESTAMP = "x-timestamp";
    public static final String SIGN_BLANK = "Sign ";
    public static final String STR_SUCCESS = "SUCCESS";
    public static final long LONG_1000 = 1000L;
    public static final String SHA_512 = "SHA-512";
    public static final String UTF_8 = "UTF-8";
    public static final String STR_MSG = "msg";
    public static final String SUCCESS_CODE_0000 = "0000";
    public static final String ZTEIMES_BYTE_DANCE_SERVERQUALITYCODE = "ZTE-SF-GetLogisticsTracking";
    public static final String IWMS_DEPENDENT_SERVICE_ERROR = "iwms_dependent_service_error";
    public static final String STR_1001 = "1001";
    public static final String B2B_PULL_ERROR = "推送B2B数据异常";
    public static final String BAIDU_STOCK_UPLOAD_ERROR_DATA = "百度部件库存数据上传异常数据";
    public static final String FACE_TO_CUSTOMER_TRAJECTORY_QUERY = "faceToCustomerTrajectoryQuery";
    public static final String METHOD = "method";
    public static final String WAYBILL_NO = "waybillNo";
    public static final String QCL = "QCL";
    public static final String BILL_LINE_NUMBER  = "00001";
    public static final String STR_LPAD = "%05d";
    public static final String INVALID_JSON_STRUCTURE = "无效的JSON结构：缺少或不正确的“jsonCode”字段";
    public static final String FAILED_TO_PARSE_JSON_RESPONSE = "无法解析JSON响应";
    public static final String JSON_PARSING_RESULTED_IN_NULL = "JSON解析导致为null";
    public static final String LOOKUP_TYPE_1000056= "1000056";
    public static final String ZTE_ISS_BARCODECENTER_SPLIT_REEL = "/ewms/barcode/split";
    public static final String EWMS = "ewms";
    public static final String PARENT_BARCODE = "parentBarcode";
    public static final String SPLIT_CHILD_DTOS = "splitChildDTOS";
    public static final String ZTE_INFOR_MOVE_SPLIT_REEL="/zte-scm-infor-move/infor/splitreelid/splitReelId";
    public static final String IQCCSZZ="IQCCSZZ";
    public static final String ROOT="root";
    public static final String SCPRD="SCPRD_";
    public static final String CATEGORYCODE="categoryCode";
    public static final String COUNT="count";
    public static final String ZTE_ISS_BARCODECENTER_BLANKGENERATE = "/barcode/blankGenerate";
    public static final String ZTEPKGPKGID="ZTEPKG_PKGID";
    public static final String EWMS_UPPER = "EWMS";
    public static final String ZTE_INFOR_MOVE_SPLIT_PKG="/zte-scm-infor-tridimensional/Infor/interactive/move";
    public static final String INFOR_SUCCESS = "200";
    public static final String IQC_EXPORT_EX_NAME= "IQC质检领料单明细";
    public static final String IQC_EX_EXPORT= "IQC质检领料单明细导出";
    public static final String SCATTEROUT_WEBSERVICE_CODE = "WMS_M07_MachineScatterOutSrv";
    public static final String LOOKUP_TYPE_1000058= "1000058";
    public static final String INFOR_IQCFAIL_MATERIAL_MONITOR= "IQC质检失败超4次信息";
    public static final String INFOR_IQCFAIL_MATERIAL_MONITOR_NAME= "IQC质检失败超4次告警";
    public static final String INFOR_IQCFAIL_MATERIAL_FAILED="IQC质检失败超4次发送邮件失败";
    public static final String LOOKUP_TYPE_1000060= "1000060";
    public static final String LOOKUP_TYPE_1000097= "1000097";
    public static final String LOOKUP_TYPE_1000061= "1000061";
    public static final String LOOKUP_TYPE_1000062= "1000062";
    public static final String LOOKUP_TYPE_1000063= "1000063";
    public static final String LOOKUP_TYPE_1000064= "1000064";
    public static final String LOOKUP_TYPE_1000065= "1000065";
    public static final String LOOKUP_TYPE_1000095= "1000095";
    public static final String WAREHOUSE_ROAD_WORK_NAME= "路网数据清单";
    public static final String WAREHOUSE_ROAD_WORK_EXPORT= "路网数据导出";
    public static final String WAREHOUSE_ALGORITHM_NAME= "算法方案清单";
    public static final String WAREHOUSE_ALGORITHM_EXPORT= "算法方案导出";
    public static final String ALGORITHM_RESULT_NAME= "算法计算结果清单";
    public static final String ALGORITHM_RESULT_EXPORT= "算法计算结果导出";
    public static final String EA = "EA";
    public static final String STORERKEY_ZTE = "ZTE";
    public static String JOB_AUTO_BOMSCRAP_BILL = "JOB_AUTO_BOMSCRAP_BILL";
    public static final String BOMSCRAP_MAKE_ERROR = "单板报废产生入库单定时任务失败";
    public static final String JSON_MESSAGE = "message";
    public static final String STR_APP_CODE = "appcode";
    public static final String STR_SECRET_KEY = "secretKey";
    public static final List<String> LOOKUP_TYPE_OVERDUE_MATERIAL_MONITOR = Arrays.asList("1000057", "1000059");
    public static final String LOOKUP_TYPE_1000057= "1000057";
    public static final String LOOKUP_TYPE_1000059= "1000059";
    public static final String IS_PRODPLAN = "isProdplan";
    public static final String PLAN_ORG_NAME = "南京";
    public static final String STR_NUMBER_TEN = "10";
    public static final String SPLITTER = "|";
    public static final String SPLITTER_ESCAPE = "\\|";
    public static final String OVERDUE_REMARK = "未维护默认仓";
    public static final String STR_WMWHSE26 = "WMWHSE26";

    public static final String REVIEW_ENDED = "-review-ended";
    public static final String REVIEW_TURN_DOWN = "-review-turn-down";
    public static final String UPDATE_DELAY_URL="/delayCheck/updateDelayRecheckBillByNo";
    public static final String SUBMIT_DETAIL_URL="/delayCheck/submitDetail";
    public static final String SUBMIT_RECHECK_BILL_URL="/delayCheck/submitRecheckBill";
    public static final String RECHECK_NO = "recheckNo";
    public static final String NEW = "NEW";
    public static final String OPER_TYPE = "operType";
    public static final String MANUAL = "MANUAL";
    public static final String REVIEW_TOPIC = "zte-iss-review";

    public static final String REVIEW_KEY = "messageKey";
    public static final String IS_APPROVAL = "isApproval";
    public static final String STR_YYYY_MM_DD = "yyyyMMdd";
    public static final String MES = "MES";
    public static final String RED_DOT_TASK = "冻结失败红点通知";
    public static final String B25 = "B25";
    public static final String DISTR01 = "DISTR01";

    public static final String DISTR08 = "DISTR08";
    public static final String AUTOMATIC_ORDER = "自动建单";
    public static final String WM_WHSE_NAME = "仓库";
    public static final String EXTERNAL_KEY = "外部单号";
    public static final String SOURCE_KEY = "ASN/SO单号";
    public static final String ASN_SOURCE_KEY = "ASN单号";
    public static final String SO_SOURCE_KEY = "SO单号";
    public static final String MATERIAL_CODE = "物料代码";
    public static final String MATERIAL_BARCODE = "物料条码";
    public static final String QUARANTINE_REASON = "隔离原因";
    public static final String SEMICOLON = "：";
    public static final String CHINESE_COMMA = "，";
    public static final String LOWERCASE_SYSTEM = "system";
    public static final String STR_RECEIVED_COMPLETED = "收货完成";
    public static final String STR_RECEIVED_DOING = "收货中";
    public static final String WHSEID_PRODUCTBASE = "1000071";
    public static final String DISTR02 = "DISTR02";
    public static final String RED_DOT_TASK_ESCC = "退供应商主体扫描失败通知";
    public static final String RED_DOT_BOX_SN_BIND = "SN与箱ID未绑定监控预警";
    public static final String RETURN_APPLYNO = "退货申请单";
    public static final String CREATOR = "制单人";
    public static final String LOOKUP_TYPE_1000072 = "1000072";
    public static final String SOURCE_LOCATION = "源仓库";
    public static final List<String> LOOKUP_TYPE_RED_DOT = Arrays.asList("1000059","1000071", "1000076", "1000077");
    public static final String LOOKUP_TYPE_1000077 = "1000077";
    public static final String LOOKUP_TYPE_1000071 = "1000071";
    public static final String LOOKUP_CODE_100007600001 = "100007600001";
    public static final String LOOKUP_CODE_100007600002 = "100007600002";
    public static final String LOOKUP_CODE_100007600003 = "100007600003";
    public static final String LOOKUP_CODE_100007600004 = "100007600004";
    public static final String ALLOCATION_TIMEOUT = "allocationTimeout";
    public static final String TIMEOUT = "timeout";
    public static final String ALLOCATION_ERROR = "下达超时，请处理；";
    public static final String ALLOCATION_RED = "订单下达超时_";
    public static final String DISTR04 = "DISTR04";
    public static final String EQP_NAME= "eqpName";
    public static final String DESC= "desc";
    public static final String ORDER= "订单";
    public static final String LINENUMBER= "行号";
    public static final String OVER_TIME_RED = "物料超有效期_";
    public static final String OVER_TIME_ERROR = "三次超期不可发料，请处理；";
    public static final String ITEM_NO_NAME = "物料代码";
    public static final String BARCODE_NAME = "条码";
    public static final String PICKING_TIMEOUT = "拣货超时_";
    public static final String PICKING_TIMEOUT_ERROR = "拣货超时，请处理；";
    public static final String REPLENISH = "订单待补货_";
    public static final String REPLENISH_ERROR = "需要补货再拣货，请处理；";
    public static final String MATERIAL_SHORTAGE = "需计调核实缺料_";
    public static final String MATERIAL_SHORTAGE_ERROR = "库存缺料，订单分配失败，请处理；";
    public static final String MATERIAL_SHORTAGE_SUPPLIER = "物料待供应商送货_";
    public static final String MATERIAL_SHORTAGE_SUPPLIER_ERROR = "待供应商送货，请处理；";
    public static final String MATERIAL_SHORTAGE_ALLOT = "物料待调拨收货_";
    public static final String MATERIAL_SHORTAGE_ALLOT_ERROR = "待调拨入库，请处理；";
    public static final String FREEZE = "解除冻结发料_";
    public static final String FREEZE_ERROR = "订单分配失败，请处理；";
    public static final String QTY_HOLD = "库存隔离";
    public static final String CHECKOUT = "物料待检验/入库_";
    public static final String CHECKOUT_ERROR = "待检验/入库，请处理；";
    public static final int INT_RED_4 = 4;
    public static final int INT_RED_5 = 5;
    public static final List<String> ORDER_TYPE_RED_DOT = Arrays.asList("100", "110", "120","130", "131", "140","432", "430", "420", "461");
    public static final List<String> HOLD_CHECK_TYPE = Arrays.asList("QCFAILED", "RECHOLD");
    public static final String ERROR_REASON = "errorReason";
    public static final String ORDER_TIMEOUT_ALLOT = "订单超时分配";
    public static final String REPLENISH_TITLE = "待补货";
    public static final String MATERIAL_SHORTAGE_TITLE = "缺料";
    public static final String TAKE_OVER = "待收货";
    public static final String EDI_ZMD_PO_S = "EDI_ZMD_PO_S";
    public static final String EDI_ZMD_SO_S = "EDI_ZMD_SO_S";
    public static final String TRANSFER_STOCK_RECEIVE_FOR_INFOR = "transferStockReceiveForInfor";
    public static final String X_TIME_STAMP = "X-Time-Stamp";
    public static final String X_SYSTEM_CODE = "X-System-Code";
    public static final String X_ECCN_TOKEN = "X-ECCN-Token";
    public static final String ECCN_VERSION_ECCN_URL = "/ZXGRC-ECCN300/ECCN/zte-grc-eccn-eccnsearch/eccnApi/versionEccn";
    public static final List<String> LOOKUP_TYPE_RED_DOT_REPORT = Arrays.asList("1000071", "1000074", "1000075");
    public static final String LOOKUP_TYPE_1000074 = "1000074";
    public static final String LOOKUP_TYPE_1000075 = "1000075";
    public static final String RED_DOT_NAME = "INFOR出库任务红点";
    public static final String RED_DOT_EXPORT = "INFOR出库任务红点清单";
    public static final String OVER_TIME_CHECK = "overTimeCheck";
    public static final String IS_TIME_OUT = "isTimeOut";
    public static final String ALLOC_ERROR = "allocError";
    public static final List<String> LOOKUP_TYPE_EXECUTE_RED_DOT = Arrays.asList("1000071", "1000076");
    public static final String QT = "qt";
    public static final String QT_NAME = "其他";
    public static final List<String> LOOKUP_TYPE_MSL_SERVICE_LIFE = Arrays.asList("1000071", "1000078", "1000079");
    public static final String LOOKUP_TYPE_1000078 = "1000078";
    public static final String LOOKUP_TYPE_1000079 = "1000079";
    public static final String LOOKUP_CODE_100007900001 = "100007900001";
    public static final BigDecimal BIG_DECIMAL_1 =new BigDecimal(1);
    public static final String STOCK_FLOW_URL = "/composerapi/wms/openApi/getStockFlowByRid";
    public static final String MSL_SERVICE_LIFE_NAME = "潮敏物料车间寿命导出";
    public static final String MSL_SERVICE_LIFE_EXPORT = "潮敏物料车间寿命清单";
    public static final String LOOKUP_TYPE_1000076 = "1000076";
    public static final String MSL_SERVICE_LIFE_RED = "潮敏器件车间寿命_";
    public static final String NO_NAME = "代码";
    public static final String REELID = "Reelid";
    public static final String MSL_SERVICE_LIFE = "潮敏车间寿命告警，请处理；";
    public static final String DISTR06 = "DISTR06";
    public static final String LOOKUP_CODE_100005900001= "100005900001";
    public static final String LOOKUP_CODE_100005900002= "100005900002";
    public static final String ACCEPT = "Accept";
    public static final String EXTRAFIELDS = "extraFields";
    public static final String CUSTOMER_SUPPLY_TYPE = "customerSupplyType";
    public static final String MODULES = "modules";
    public static final String ECCN = "ECCN";
    public static final String FIELD_NAME = "fieldName";
    public static final String ITEM_NO = "itemNo";
    public static final String FIELD_TYPE = "fieldType";
    public static final String IN = "IN";
    public static final String FIELD_VALUE = "fieldValue";
    public static final String GET_ONHAND_QTY = "getOnhandQty";
    public static final String IWMS = "IWMS";
    public static final String ZTE_IWMS_MANAGE = "zte-iwms-manage";
    public static final String SUBINVENTORYCODE = "subinventoryCode";
    public static final String ORGANIZATION_ID = "organizationId";

    public static final String SOURECE_IWMS = "IWMS";
    public static final String SOURECE_IDA = "IDA";
    public static final String PRODUCT_BASE = "生产基地";
    public static final List<String> LOOKUP_TYPE_QC = Arrays.asList("1000072","1000082","1000083","1000080");
    public static final String LOOKUP_TYPE_1000073 = "1000073";
    public static final String LOOKUP_TYPE_1000082 = "1000082";
    public static final String LOOKUP_TYPE_1000083 = "1000083";
    public static final String QC_PROJECT = "QC项目";
    public static final String RESPONSIBLE_DEPARTMENT = "责任部门/科室/班组";
    public static final String LIABILITY = "责任人";
    public static final String PROBLEM_DESC = "巡检问题描述";
    public static final String DISTR05 = "DISTR05";
    public static final String RED_DOT_INSPECTION = "巡检问题红点通知";
    public static final String QUALITY_INSPECTION = "数字QC任务信息";
    public static final String LOOKUP_CODE_************ = "************";
    public static final String LOOKUP_CODE_************= "************";
    public static final String WARNING_DAYS = "warningDays";
    public static final int INT_87 = 87;
    public static final String ONGOING = "ongoing";
    public static final String IMPORTED = "imported";
    public static final String INONE_X_ACCOUNT_ID = "X-Account-Id";
    public static final String CREATE_TIME = "create_time>=";
    public static final String GET_IDA_INFOURL = "/v1/app/APP0809830803365687297/page/PAGE0956912745768001537/batchQueryInstanceData";
    public static final String LOOKUP_TYPE_1000080 = "1000080";
    public static final String IDA_ID = "id";
    public static final String IDA_MODIFYED = "整改完成";
    public static final String STR_BM = "bm";
    public static final String PROBLEM_TYPE = "问题严重程度";
    public static final String NOT_RECEIVED = "未收货";
    public static final String ESBMULE_URL = "/esbmule/services/query/TO_OTHER_WL";
    public static final String USER_CODE = "usercode";
    public static final String PASSWARD = "password";
    public static final String ESB = "ESB";
    public static final String ESB_DATA = "DATA";
    public static final String DATA_INFOS = "DATAINFOS";
    public static final String DATA_INFO = "DATAINFO";
    public static final String DATA_CODE = "CODE";
    public static final String DATA_RESULT = "RESULT";
    public static final String DATA_INFO_IS_EMPTY = "没有查询到数据！";
    public static final String DATA_PROPERTYVALUE = "PROPERTYVALUE";
    public static final String DATA_OSP = "OSP";
    public static final String DATA_IMES300 = "iMES300";
    public static final String DATA_WARD = "xvkBUMcPVR5EjdHAvEnZbjIgBXqmkX6waMvmrwIXBS3WB5Yl";
    public static final String LOOKUP_TYPE_1000087 = "1000087";
    public static final String DATA_CODE_005304 = "005304";
    public static final String DATA_CODE_004000 = "004000";
    public static final String DATA_CODEVALUE = "CODEVALUE";
    public static final String DATA_SPECIALITY = "SPECIALITY";
    public static final String DATA_SPECIALITYCODE = "SPECIALITYCODE";
    public static final String DATA_PROPERTY = "PROPERTY";
    public static final String DATA_PROPERTYCODE = "PROPERTYCODE";
    public static final String PRODUCT_DATE_ISEMPTY = "生产日期为空";

    public static final String ZTE_SCM_INFOR_ALGORITHM = "zte-scm-infor-algorithm";
    public static final String CAL_SHORTE_STROUTE_URL = "/infor/calReplenishRoute";

    public static final String WAREHOUSE_REPLENISHMENT_PATH_NAME= "补货路径清单";
    public static final String WAREHOUSE_REPLENISHMENT_PATH_EXPORT= "补货路径导出";
    public static final String STR_SUBMITED= "SUBMITED";
    public static final String STR_APPROVALING= "APPROVALING";
    public static final String STR_APPROVED= "APPROVED";
    public static final String STR_NORMAL= "normal";
    public static final String STR_ODMT= "ODMT";
    public static final String STR_B = "B";
    public static final String STR_M = "M";
    public static final String STR_P = "P";
    public static final String ALIBABA = "alibaba";
    public static final String ALIBABA_PUBLIC_CLOUD = "ALIBABA_PUBLIC_CLOUD";
    public static final String ZTE101_NG01 = "ZTE101_NG01";
    public static final String DEDUCTION_PLAN_INFO_ZH = "部件出入库计划申请&执行&核销";
    public static final String LOOKUP_TYPE_1000091 = "1000091";
    public static final String LOOKUP_CODE_100009100001 = "100009100001";
    public static final String LOOKUP_CODE_100009100002 = "100009100002";
    public static final String LOOKUP_CODE_100009100003 = "100009100003";
    public static final String LOOKUP_CODE_100009100004 = "100009100004";
    public static final String LOOKUP_CODE_100009100006 = "100009100006";
    public static final String LOOKUP_CODE_100009100008 = "100009100008";
    public static final String ALI_REMARK = "不良品退供应商";
    public static final String LOOKUP_CODE_100008800002 = "100008800002";
    public static final String LOOKUP_CODE_100009100005 = "100009100005";
    public static final String LOOKUP_CODE_100009100007 = "100009100007";
    public static final String LOOKUP_CODE_100009100010 = "100009100010";
    public static final String LOOKUP_CODE_100009100012 = "100009100012";

    public static final String MIXED_BOX_PKGID_BOUND_SN_ZH = "混箱绑定箱包与SN";
    public static final String ORIGIN_BOX_BOUND_SN_UPDATE_ZH = "原箱新增或取消箱包与SN绑定关系";
    public static final String LOOKUP_CODE_100009100014 = "100009100014";
    public static final String LOOKUP_CODE_100009100011 = "100009100011";
    public static final String ALI_STOCK_ZH = "阿里库存现有量";
    public static final String STR_SERVER ="server";
    public static final String STR_CREATE_CARTON_RELATION_LIST ="create_carton_relation_List";
    public static final String ALI_INVENTORY_SYNC_ZH = "阿里库存交易明细";
    public static final String STR_CATEGORY ="category";

    public static final String SOURCE_SYSTEM_FROM_INFOR = "1";
    public static final String SOURCE_SYSTEM_FROM_REPAIR = "2";
    public static final String FLOOR = "楼栋";
    public static final String HELP_TYPE = "帮助类型";
    public static final List<String> LOOKUP_TYPE_FLOOR = Arrays.asList("1000072","1000073","1000092");
    public static final String RED_DOT_FLOOR = "智能接收呼叫帮助红点任务";
    public static final String DISTR07 = "DISTR07";
    public static final String DELIVER_INFO_EMPTY = "未查询到送货单信息！";

    public static final String INVENTORY_TYPE_GOOD = "0";
    public static final String INVENTORY_TYPE_BAD = "5";

    public static final String ALIBABA_FACTORY_CODE = "ZTE101";

    public static final String INVENTORY_DIFF_FEEDBACK_EX_NAME= "库存差异反馈清单";
    public static final String INVENTORY_DIFF_FEEDBACK_EX_EXPORT= "库存差异反馈清单导出";

    public static final String SAVED_CHINESE = "已保存";
    public static final String SUBMITED_CHINESE = "已提交";
    public static final String  STOCK_INFO_BY_WAREHOUSEIDS  = "/StockController/getStockInfoByWarehouseIds";
    public static final String CREATE_BILL = "创建调拨单";
    public static final String BIZ_SCENES = "odmTransfer";
    public static final String TRANSFER_TYPE = "sub_transfer";
    public static final String CREATE_TRANSFER = "Create_Transfer";
    public static final String MOVE = "Move";
    public static final String ORIGIN_BOX = "originalBox";
    public static final String MIX_BOX = "mixBox";
    public static final String NON_CONTROL = "nonControl";

    public static final String DEDUCTION_INFO_ZH = "工单实际扣料";

    public static final String ZTE101_CO01 = "ZTE101_CO01";

    public static final String LOOKUP_CODE_100009100009 = "100009100009";
    public static final String SN_INVENTORY_INFO_ZH = "部件箱包&SN库存同步";
    public static final String B2B = "B2B";

    public static final String FACTORY_CODE = "factory_code";
    public static final String JIAS_YANG_DING_ZHI = "JayYangDingZhi";
    public static final String SPOT_CHECK= "SPOTCHECK";


    public static final String TRANS_TYPE = "organization_transfer";
    public static final String SN = "SN";
    public static final String MIX = "mix";
    public static final String ORIGINAL = "original";
    public static final String BOX = "BOX";
    public static final String INVENTORY_DIFF_DATA_NAME = "库存差异数据清单";
    public static final String INVENTORY_DIFF_DATA_EXPORT = "库存差异数据导出";
    public static final String PARTS_RECEIVED_WAREHOUSE = "部件接收入库";
    public static final String TICKET_TYPE_ASN = "ASN";
    public static final String INVENTORY_DIFF_FEEDBACK="供应商库存Gap责任归属信息反馈";
    public static final String PRODUCT_INVENTORY_URL="/zte-mes-manufactureshare-centerfactory/alibabaPushController/OnhandQtyPush";
    public static final String LOOKUP_TYPE_CATEGORY_NAME = "1000093";
    public static final String BOX_NO = "箱号";

    public static final String BOX_TYPE = "箱类型";

    public static final String LOC = "库位";

    public static final String PUT_AWAY_ZONE = "库区";

    public static final String ZONE_TYPE = "库区类型";

    public static final String BOX_QTY = "箱库存";

    public static final String SN_QTY = "SN库存";

    public static final String MPN = "MPN";
    public static final String ZTE_INSTOCK_QTY = "ZTE配送库存";

    public static final String ZTE_ONLINE_QTY = "ZTE维修库库存";

    public static final String ALIBABA_STOCK_QTY = "阿里库存";

    public static final String DIFF_STOCK_QTY = "库存差异数量";

    public static final String RED_DOT_BOX_TYPE = "箱类别和库区不一致监控告警";

    public static final String RED_DOT_MIXED_BOX_INVENTORY = "混箱区库存不一致监控告警";


    public static final String RED_DOT_ZTE_ALIBABA_INVENTORY = "ZTE&阿里库存不一致监控告警";

    public static final String EXTERNAL_ITEM_SPLIT = "/ZXISS_iSRM300/UI/zte-scm-iscp-bff-service/external/item/split";
    public static final String EXTERNAL_ITEM_INFO = "/ZXISS_iSRM300/UI/zte-scm-iscp-bff-service/external/item";
    public static final List<String> ISRM_MODULES = Arrays.asList("SITUATION","ECCN");
    public static final String DATABASE_NAME = "数智QC基础信息";
    public static final String OBJECT_VALUE_NAME = "数智QC对象值信息";
    public static final String FORMAT_ERROR = "工号格式不符，请检查";
    public static final String EMPNO_FORMAT = "^(?:(?=.*[\\u4e00-\\u9fa5])(?=.*\\d)[\\u4e00-\\u9fa5\\d]+)(?:,(?=.*[\\u4e00-\\u9fa5])(?=.*\\d)[\\u4e00-\\u9fa5\\d]+)*$";
    public static final String DELIVER_INFO_RECEIVING = "送货单处于收货状态中，暂时无法打印！";


    public static final String GOOD_QUALITY_COMPONENTS = "部件良品";
    public static final String COMPLETE_MACHINE = "整机";
    public static final String IN_PRODUCTION = "在制";
    public static final String DEFECTIVE_PRODUCT = "整机不良品";
    public static final String BUFFER_STOCK = "Buffer库";
    public static final String COMPONENTS_STOCK = "部件大库";

    public static final String LOOKUP_CODE_100008800001 = "100008800001";

    public static final String LOOKUP_CODE_100008800004 = "100008800004";
    public static final String LOOKUP_CODE_100008800005 = "100008800005";
    public static final String LOOKUP_CODE_100008800006 = "100008800006";
    public static final String PICKING_INFO = "拆零拣货清单";
    public static final String PICKING_INFO_EXPORT = "拆零拣货清单导出";
    public static final String LOOKUP_TYPE_1000098 = "1000098";
    public static final String EXTERNALORDERKEY2= "外部单号2";
    public static final String STR_WMWHSE11 = "WMWHSE11";
    public static final String STR_NORMAL2= "Normal";
    public static final String TEST_MODE_IN_INSPECTION= "In_Inspection";
    public static final String IS_ENABLE_LEAD = "isEnableLead";
    public static final String WM_WHSE_ID = "itemWhseId";
    public static final String SUM_QTY = "sumQty";
    public static final String REQ_QTY = "reqQty";
    public static final String FLAG = "FLAG";
    public static final String QTY_DIFFERENCE = "qtyDifference";
    public static final String IS_OCCUPATION = "isOccupation";
    public static final String UPPER_WHSE_ID = "upperWhseId";

    // 备件该配件配置项
    public static final String LOOKUP_TYPE_SUBMIT_WAREHOUSE="1000101";
    public static final String LOOKUP_CODE_SUBMIT_WAREHOUSE_ID="100010100001";
    public static final String LOOKUP_CODE_SUBMIT_STOCK_ID="100010100002";
    public static final String LOOKUP_CODE_SUBMIT_LOCATION_ID="100010100003";
    public static final String LOOKUP_TYPE_SUBMIT_WAREHOUSE_ALERT_EMAIL="1000102";
    public static final String INFOR_SUBMIT_STORAGE_ERROR= "备件改配件单据推送仓储中心出错";
    public static final String INFOR_SUBMIT_WMS_ERROR= "备件改配件单据推送WMS出错";

    public static final String SUBMIT_BILL_STATUS_0030 ="0030";
    public static final String SUBMIT_BILL_TYPE ="LXRKST";
    public static final String SUBMIT_BILL_TYPE_CODE ="LXRK_SCCLK_NERP";
    public static final String LOOKUP_CODE_100010300001 = "100010300001";
    public static final String PENDINGIN = "PendingIn";
    public static final String PENDINGOUT = "PendingOut";

    public static final String STR_PERCENTAGE_ZERO = "0%";
    public static final String NEW_CREATE = "新建";

    // 美团库存上传常量
    public static final String CUSTOMER_NAME_MEITUAN = "MEITUAN";
    public static final String COOPERATION_MODE_AVAP = "AVAP";

}