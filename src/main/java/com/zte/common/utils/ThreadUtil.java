package com.zte.common.utils;

import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
public class ThreadUtil {

    private static final Integer CORE_POOL_SIZE = 5 ;
    private static final Integer MAXIMUM_POOL_SIZE = 20 ;
    private static final Long KEEP_ALIVE_TIME = 60L ;
    private static final Integer ONE = 1 ;
    public static int MAX_QUEUE = 3000;
    public static final ThreadPoolExecutor WEBSERVICE_INTERFACE_MONITORING =
            new ThreadPoolExecutor(CORE_POOL_SIZE,MAXIMUM_POOL_SIZE,KEEP_ALIVE_TIME, TimeUnit.SECONDS,
                    new LinkedBlockingQueue<Runnable>(MAX_QUEUE),new NameTreadFactory());
    public static final ThreadPoolExecutor INVENTORY_HOLD_RECORD_EXECUTOR =
            new ThreadPoolExecutor(CORE_POOL_SIZE,MAXIMUM_POOL_SIZE,KEEP_ALIVE_TIME, TimeUnit.SECONDS,
                    new LinkedBlockingQueue<Runnable>(MAX_QUEUE),new NameTreadFactory());
    public static final ThreadPoolExecutor OVERDUE_RECHECK_EXECUTOR =
            new ThreadPoolExecutor(CORE_POOL_SIZE,MAXIMUM_POOL_SIZE,KEEP_ALIVE_TIME, TimeUnit.SECONDS,
                    new LinkedBlockingQueue<Runnable>(MAX_QUEUE),new NameTreadFactory());
    public static final ThreadPoolExecutor IQC_REQUISITION_EXECUTOR =
            new ThreadPoolExecutor(CORE_POOL_SIZE,MAXIMUM_POOL_SIZE,KEEP_ALIVE_TIME, TimeUnit.SECONDS,
                    new LinkedBlockingQueue<Runnable>(MAX_QUEUE),new NameTreadFactory());
    public static final ThreadPoolExecutor RED_DOT_EXECUTOR =
            new ThreadPoolExecutor(CORE_POOL_SIZE,MAXIMUM_POOL_SIZE,KEEP_ALIVE_TIME, TimeUnit.SECONDS,
                    new LinkedBlockingQueue<Runnable>(MAX_QUEUE),new NameTreadFactory());
    public static final ThreadPoolExecutor MSL_SERVICE_LIFE_EXECUTOR =
            new ThreadPoolExecutor(CORE_POOL_SIZE,MAXIMUM_POOL_SIZE,KEEP_ALIVE_TIME, TimeUnit.SECONDS,
                    new LinkedBlockingQueue<Runnable>(MAX_QUEUE),new NameTreadFactory());
    public static final ThreadPoolExecutor RED_DOT_DIGITQC =
            new ThreadPoolExecutor(CORE_POOL_SIZE,MAXIMUM_POOL_SIZE,KEEP_ALIVE_TIME, TimeUnit.SECONDS,
                    new LinkedBlockingQueue<Runnable>(MAX_QUEUE),new NameTreadFactory());
    public static final ThreadPoolExecutor ALIBABA_JOB_EXECUTOR =
            new ThreadPoolExecutor(CORE_POOL_SIZE,MAXIMUM_POOL_SIZE,KEEP_ALIVE_TIME, TimeUnit.SECONDS,
                    new LinkedBlockingQueue<Runnable>(MAX_QUEUE),new NameTreadFactory());

    public static final ThreadPoolExecutor AI_GET_LPN_INVENTORY =
            new ThreadPoolExecutor(CORE_POOL_SIZE,MAXIMUM_POOL_SIZE,KEEP_ALIVE_TIME, TimeUnit.SECONDS,
                    new LinkedBlockingQueue<Runnable>(MAX_QUEUE),new NameTreadFactory());
    static class NameTreadFactory implements ThreadFactory {

        private final AtomicInteger mThreadNum = new AtomicInteger(ONE);

        @Override
        public Thread newThread(Runnable r) {
            Thread t = new Thread(r, "my-thread-" + mThreadNum.getAndIncrement());
            System.out.println(t.getName() + " has been created");
            return t;
        }
    }

}
