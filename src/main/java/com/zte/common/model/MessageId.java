package com.zte.common.model;

/**
 * 消息ID
 * <AUTHOR>
 */
public class MessageId {

	public static final String DATE_FORMATE_ERROR = "date.formate.error";
	public static final String NO_DATA_FOUND = "no.data.found";
	public static final String DATA_UPDATE_ERROR = "data.update.error";
	// 请输入仓库
	public static final String PLEASE_INPUT_WMWHSEID = "Please.Input.WareHouse";
	// 订单号,波次号,至少要有一个输入条件
	public static final String PLEASE_INPUT_ORDERKEY_WAVEKEY = "Please.Input.OrderKey.WaveKey";
	// 请输入箱号
	public static final String PLEASE_INPUT_ID = "Please.Input.Id";
	public static final String THIS_METHOD_HAD_BEEN_RUNNING = "this.method.had.been.running";
	public static final String CAN_NOT_GET_KEY_VALUE = "can.not.get.key.value";
	public static final String PLEASE_INPUT_KEY_VALUE = "please.input.key.value";
	// SO单号,外部单号2,波次号,落放ID,至少要有一个输入条件
	public static final String PLEASE_INPUT_SO_EX_WAVE_ID = "Please.Input.OrderKey.ExternalOrderKey2.WaveKey.Id";
   //订单号,外部订单号2,至少要有一个输入条件
	public static final String PLEASE_INPUT_ORDERKEY_EXTERNALORDERKEY2 = "Please.Input.OrderKey.ExternalOrderKey2";
    // 合同号超过50行，请检查谢谢！
	public static final String PLEASE_IMPORT_EXCEL_NOMORETHAN_CONTRACTNUMBER_FIFTY = "Please.Import.Excel.NoMoreThan.Contractnumber.Fifty";
	// 发货指令号超过500行，请检查谢谢！
	public static final String PLEASE_IMPORT_EXCEL_NOMORETHAN_DOID_FIVEHUNDRED = "Please.Import.Excel.NoMoreThan.Doid.FiveHundred";
	// 装箱单号超过500行，请检查谢谢！
	public static final String PLEASE_IMPORT_EXCEL_NOMORETHAN_BILLNUMBER_FIVEHUNDRED = "Please.Import.Excel.NoMoreThan.BillNumber.FiveHundred";
	// 文件格式错误，请使用excel导入！
	public static final String PLEASE_CHECK_IMPORT_FILE_TYPE_EXCEL = "Please.Check.Import.File.Type.Excel";
	// 没有可以捞取的单号数据
	public static final String NO_NUMBER_RETRIEVED = "There.Is.No.Order.Number.To.Be.Retrieved.";
	public static final String WHSEID_CAN_NOT_BE_EMPTY="whseid.can.not.be.empty";
	public static final String INVOKE_SUCCESS="invoke.success";
	public static final String INVOKE_FAILED="invoke.failed";
	public static final String UPDATA_SUCCESS="updata.success";
	public static final String DROPID_AND_PRODUCENO_NOT_EMPTY="dropid.and.produceno.not.empty";
	public static final String SYNC_ECCN_LOOKUP_CODE_NOT_CONFIG = "sync.eccn.lookupcode.not.config";
	public static final String SKU_REELID_WMWHSE_IS_NOT_EMPTY="sku.reelid.wmwhse.is.not.empty";
	public static final String SKU_REELID_WMWHSE_IS_NOT_VAL1="sku.reelid.wmwhse.is.not.val1";
	public static final String SKU_REELID_WMWHSE_IS_NOT_VAL2="sku.reelid.wmwhse.is.not.val2";
	public static final String SKU_REELID_SKU_IS_NOT_EMPTY="sku.reelid.sku.is.not.empty";
	public static final String SKU_REELID_SKU_IS_NOT_VAL1="sku.reelid.sku.is.not.val1";
	
	public static final String INFOR_FALLBACK_PRICE_NO_DATA="infor.fallback.price.no.data";
	public static final String INFOR_FALLBACK_PRICE_FALLBACK_NO_EMPTY="infor.fallback.price.fallback.no.empty";
	public static final String INFOR_FALLBACK_PRICE_FALLBACK_DETAIL="infor.fallback.price.fallback.detail";
	public static final String INFOR_FALLBACK_PRICE_DETAIL_NOT_VAL1="infor.fallback.price.detail.not.val1";
	public static final String INFOR_FALLBACK_PRICE_DETAIL_NOT_VAL2="infor.fallback.price.detail.not.val2";
	public static final String INFOR_FALLBACK_PRICE_DETAIL_NOT_VAL3="infor.fallback.price.detail.not.val3";
	public static final String INFOR_FALLBACK_PRICE_DETAIL_NOT_VAL4="infor.fallback.price.detail.not.val4";
	public static final String INFOR_FALLBACK_PRICE_DETAIL_NOT_VAL5="infor.fallback.price.detail.not.val5";
	public static final String INFOR_FALLBACK_PRICE_DETAIL_NOT_VAL6="infor.fallback.price.detail.not.val6";
	public static final String INFOR_FALLBACK_PRICE_DETAIL_NOT_VAL7="infor.fallback.price.detail.not.val7";
	public static final String INFOR_FALLBACK_PRICE_DETAIL_NOT_VAL8="infor.fallback.price.detail.not.val8";
	public static final String INFOR_FALLBACK_PRICE_DETAIL_NOT_VAL9="infor.fallback.price.detail.not.val9";
	public static final String INFOR_FALLBACK_PRICE_DETAIL_NOT_VAL10="infor.fallback.price.detail.not.val10";
	public static final String INFOR_FALLBACK_PRICE_DETAIL_NOT_VAL11="infor.fallback.price.detail.not.val11";
	public static final String INFOR_FALLBACK_PRICE_IS_EXIST="infor.fallback.price.is.exist";
	public static final String INFOR_FALLBACK_PRICE_NO_SAMPLE_WITH_INFOR="infor.fallback.price.no.sample.with.infor";
	public static final String INFOR_FALLBACK_PRICE_QUERY_NO_DATA="infor.fallback.price.query.no.data";
	public static final String INFOR_FALLBACK_PRICE_DATA_CAN_NOT_SAMPLE="infor.fallback.price.data.can.not.sample";
	public static final String INFOR_FALLBACK_APPLY_BILL_NOT_EXIST="infor.fallback.applybill.not.exist";
	public static final String INFOR_FALLBACK_SYSLOOKUP_VALUES_NOT_EXIST="infor.fallback.syslookup.values.not.exist";


	public static final String INFOR_VMI_SO_PRICE_NOT_VAL1="infor.vmi.price.billNo.can.not.empty";   
	public static final String INFOR_VMI_SO_PRICE_NOT_VAL2="infor.vmi.price.serailKey.can.not.empty";   
	public static final String INFOR_VMI_SO_PRICE_NOT_VAL3="infor.vmi.price.exlineNo.can.not.empty";   
	public static final String INFOR_VMI_SO_PRICE_NOT_VAL4="infor.vmi.price.itemBarcode.can.not.empty";   
	public static final String INFOR_VMI_SO_PRICE_NOT_VAL5="infor.vmi.price.currencyType.can.not.empty";   
	public static final String INFOR_VMI_SO_PRICE_NOT_VAL6="infor.vmi.price.uniPrice.can.not.empty";   
	public static final String INFOR_VMI_SO_PRICE_NOT_VAL7="infor.vmi.price.uniPriceNoTax.can.not.empty";   
	public static final String INFOR_VMI_SO_PRICE_NOT_VAL8="infor.vmi.price.price.can.not.empty";   
	public static final String INFOR_VMI_SO_PRICE_NOT_VAL9="infor.vmi.price.priceNoTax.can.not.empty";   
	public static final String INFOR_VMI_SO_PRICE_NOT_VAL10="infor.vmi.price.exchangeRate.can.not.empty";   
	public static final String INFOR_VMI_SO_PRICE_NOT_VAL11="infor.vmi.price.taxRate.can.not.empty";  
	public static final String INFOR_VMI_SO_PRICE_NOT_VAL12="infor.vmi.price.has.updated";
	public static final String INFOR_VMI_SO_PRICE_NOT_VAL13="infor.vmi.price.not.existed";
	public static final String INFOR_VMI_SO_PRICE_NOT_VAL14="infor.vmi.price.data.can.not.empty";
	public static final String INFOR_DISPENSE_SERIAL_WHSEID_VAL="infor.dispense.serial.whseid.val";
	public static final String INFOR_DISPENSE_SERIAL_RECEIPT="infor.dispense.serial.receipt";
	public static final String INFOR_NO_WAIT_FEED_APPLYNO="infor.no.wait.feed.applyNo";
	public static final String PUSH_HZBILL_TO_STORAGECENTER_001="pushHZBillToStorageCenter.info.001";
	public static final String  BARDCODE_CENTER_VALIDATION_REGISTERBARCODE_EMPTY= "barcode.center.validation.registerBarcode.empty";
	public static final String  BARDCODE_CENTER_VALIDATION_RESPONSE_FORMAT = "barcode.center.validation.response.format";
	
	public static final String INFOR_VMI_SKU_INV_QUERY_VAL1="infor.vmi.invquery.itemNo.cannot.empty";
	public static final String INFOR_VMI_UUID_INV_QUERY_VAL1="infor.vmi.invquery.itemNo.supplyNo.must.enter.one";
	
	
	public static final String INFOR_SOOUTDETAIL_QUERY_VAL1="infor.so.sodetail.query.cannot.empty";
	public static final String INFOR_SOOUTDETAIL_QUERY_VAL2="infor.so.sodetail.query.error.dateformat";
	public static final String INFOR_EDISOSDETAIL_QUERY_VAL1="infor.so.edisosdetail.query.cannot.empty";
	public static final String AT_LEAST_ONE_PARAMS= "enter.at.least.one.query.condition";
	public static final String DATE_FORMAT="infor.controller.error.dateformat";
	public static final String INPUT_DATA_CAN_NOT_NULL="input.data.can.not.null";
	public static final String CAN_NOT_BE_GREATER_THAN_FIVE_HUNDRED = "can.not.be.greater.than.five.hundred";
	
	public static final String RECHECKNO_CAN_NOT_BE_EMPTY = "recheckno.can.not.be.empty";
	public static final String RECHECKNO_NOT_EXISTS = "recheckno.not.exists";
	public static final String RECHECKNO_NOT_NEED_TO_CHECK = "recheckno.not.need.to.check";
	public static final String RECHECKNO_NO_DETAILS = "recheckno.no.details";

	public static final String SYS_LOOKUP_VALUES_1000037_NOT_EXIST="sys.lookup.values.1000037.not.exist";

	public static final String CALL_BACK_DATA_EMPTY="call.back.data.empty";
	public static final String BILL_NO_EMPTY="bill.no.empty";
	public static final String OMS_SALE_BILL_NOT_EXISTS="oms.sale.bill.not.exists";
	public static final String OMS_SALE_ECSS_LOOKUP_NOT_EXISTS="oms.sale.ecss.lookup.not.exists";
	public static final String IMWS_QCBAD_RETURN_APPLY_BILL_NOT_EXISTS="iwms.qcbad.return.apply.bill.not.exists";
	public static final String CAN_NOT_BE_GREATER_THAN_FIFTY = "can.not.be.greater.than.fifty";
	public static final String HOLD_REASON_NOT_SAMPLE_HOLD = "hold.reason.not.sample.hold";
	public static final String INVENTORY_HOLD_RECORD_EXISTS = "inventory.hold.record.exists";
	public static final String ITEM_BARCODE_NOT_EXISTS = "item.barcode.not.exists";
	public static final String BA_ITEM_NOT_EXISTS = "ba.item.not.exists";
	public static final String BA_BOM_HEAD_NOT_EXISTS = "ba.bom.head.not.exists";
	public static final String CHOOSE_INVENTORY_HOLD_RECORD_EFFECTED = "choose.inventory.hold.record.effected";
	public static final String CHOOSE_INVENTORY_HOLD_RECORD_EXPIRED = "choose.inventory.hold.record.expired";
	public static final String PLEASE_LOGIN_FIRST = "please.login.first";
	public static final String REMARK_TOO_LONG = "remark.too.long";
	public static final String CAN_NOT_BE_GREATER_THAN_FIVE_THOUSAND = "can.not.be.greater.than.five.thousand";
	public static final String SKU_DOMESTIC_INTERNAL_ENABLED_UPDATE_BY_EMPTY = "sku.domestic.internal.enable.update.by.empty";
	public static final String SKU_CAN_NOT_REPEAT = "sku.can.not.repeat";
	public static final String STOCKNO_ITEMBARCODE_CHECK_COUNT = "stockNo.itemBarcode.check.count";
	public static final String NO_PARAMS = "no.params";
	public static final String STOCKNO_CHECK = "stockNo.check";
	public static final String ITEMBARCODE_REPEAT_CHECK = "itembarcode.repeat.check";
	public static final String REQUIRE_REPEAT_IMPORT = "require.repeat.import";
	public static final String REQUIRE_REPEAT_FACTORY_ITEM = "require.repeat.factory.item";
	public static final String REQUIRE_ITEM_NOT_EXISTS = "require.item.not.exists";
	public static final String REQUIRE_FACTORY_ITEM_QTY_NOT_EXISTS = "factory.sku.domestic.qty.empty";
	public static final String REQUIRE_ITEM_LENGTH_FAIL = "require.item.length.fail";
    public static final String CHECK_INFORLOC = "check.inforloc";
    public static final String PUSH_B2B_FAILED = "push.b2b.failed";
    public static final String GET_ORDER_QUERY_FOR_INFOR_FAILED = "get.order.query.for.infor.failed";
    public static final String GET_RETURN_ORDER_QUERY_FOR_INFOR_FAILED = "get.return.order.query.for.infor.failed";
	public static final String INVENTORY_HOLD_APPROVEDBY_EMPTY = "inventory.hold.approvedby.empty";
	public static final String FAILED_TO_PROCESS_APPROVAL_CENTER_KAFKA_MESSAGE = "failed_to_process_approval_center_kafka_message";
	public static final String ITEM_TQE_STQE_NOT_EXISTS = "item.qte.stqe.not.exists";
	public static final String IQC_TEST_REQUISITION_DETAIL_NOT_VAL1="iqc.test.requisition.detail.not.val1";
	public static final String IQC_TEST_REQUISITION_DETAIL_NOT_VAL2="iqc.test.requisition.detail.not.val2";
	public static final String ITEM_NO_NOT_EXISTS = "item.no.not.exists";
	public static final String IQC_TEST_REQUISITION_REEL_NOT_NULL="iqc.test.requisition.reel.not.null";
	public static final String IQC_TEST_REQUISITION_STOCK_ERROR="iqc.test.requisition.stock.error";
	public static final String IQC_TEST_REQUISITION_QUERY_PARAM_IS_NULL="iqc.test.requisition.query.param.is.null";
	public static final String INFOR_LOC_NOT_EXISTS = "infor.loc.not.exists";
	public static final String WAREHOUSE_ROAD_WORK_EXISTS = "warehouse.road.work.exists";
	public static final String WAREHOUSE_ROAD_WORK_REPEATED = "warehouse.road.work.repeated";
	public static final String WAREHOUSE_ALGORITHM_EXISTS = "warehouse.algorithm.exists";
	public static final String WAREHOUSE_ALGORITHM_REPEATED = "warehouse.algorithm.repeated";
	public static final String ALGORITHM_SCHEME_TYPE_INCONSISTENT_EXISTS = "algorithm.scheme.type.inconsistent.exists";
	public static final String ALGORITHM_WHOLE_WAREHOUSE_STRATEGY_INCONSISTENT_EXISTS = "algorithm.whole.warehouse.strategy.inconsistent.exists";
	public static final String ALGORITHM_MAX_WAREHOUSE_ADJUST_INCONSISTENT_EXISTS = "algorithm.max.warehouse.adjust.inconsistent.exists";
	public static final String ALGORITHM_UNIT_WHOLE_WAREHOUSE_WORK_INCONSISTENT_EXISTS = "algorithm.unit.whole.warehouse.work.inconsistent.exists";
	public static final String ALGORITHM_START_COORD_X_INCONSISTENT_EXISTS = "algorithm.start.coord.x.inconsistent.exists";
	public static final String ALGORITHM_START_COORD_Y_INCONSISTENT_EXISTS = "algorithm.start.coord.y.inconsistent.exists";
	public static final String ALGORITHM_END_COORD_X_INCONSISTENT_EXISTS = "algorithm.end.coord.x.inconsistent.exists";
	public static final String ALGORITHM_END_COORD_Y_INCONSISTENT_EXISTS = "algorithm.end.coord.y.inconsistent.exists";
	public static final String ALGORITHM_EXECUTION_FREQUENCY_INCONSISTENT_EXISTS = "algorithm.execution.frequency.inconsistent.exists";
	public static final String ALGORITHM_SCHEME_TYPE_INCONSISTENT = "algorithm.scheme.type.inconsistent";
	public static final String ALGORITHM_WHOLE_WAREHOUSE_STRATEGY_INCONSISTENT = "algorithm.whole.warehouse.strategy.inconsistent";
	public static final String ALGORITHM_MAX_WAREHOUSE_ADJUST_INCONSISTENT = "algorithm.max.warehouse.adjust.inconsistent";
	public static final String ALGORITHM_UNIT_WHOLE_WAREHOUSE_WORK_INCONSISTENT = "algorithm.unit.whole.warehouse.work.inconsistent";
	public static final String ALGORITHM_START_COORD_X_INCONSISTENT = "algorithm.start.coord.x.inconsistent";
	public static final String ALGORITHM_START_COORD_Y_INCONSISTENT = "algorithm.start.coord.y.inconsistent";
	public static final String ALGORITHM_END_COORD_X_INCONSISTENT = "algorithm.end.coord.x.inconsistent";
	public static final String ALGORITHM_END_COORD_Y_INCONSISTENT = "algorithm.end.coord.y.inconsistent";
	public static final String ALGORITHM_EXECUTION_FREQUENCY_INCONSISTENT = "algorithm.execution.frequency.inconsistent";
	public static final String DELIVERY_NO_RECEIVED_NOT_EXISTS = "delivery.no.received.not.exists";
	public static final String DELIVERY_NO_RECEIVED_REPEAT = "delivery.no.received.repeat";
	public static final String PRODUCTBASE_NO_DATA_WHSE = "productbase.no.data.whse";
	public static final String TRANSFER_STOCK_RECEIVE_FOR_INFOR_FAILED = "transfer.stock.receive.for.infor.failed";
	public static final String ECCN_VERSION_ECCN_FOR_IWMS_FAILED = "eccn.version.eccn.for.iwms.failed";
    public static final String RED_DOT_STATUS_CHECK="red.dot.status.check";
	public static final String RED_DOT_EXECUTE_DATE_CHECK="red.dot.execute.date.check";
	public static final String MSL_WMWHSE_IS_NOT_EMPTY="msl.wmwhse.is.not.empty";
	public static final String MSL_SKU_IS_NOT_EMPTY="msl.sku.is.not.empty";
	public static final String MSL_LOTTABLE02_IS_NOT_EMPTY="msl.lottable02.is.not.empty";
	public static final String MSL_SERIALNUMBER_IS_NOT_EMPTY="msl.serialnumber.is.not.empty";
	public static final String CAN_NOT_FIND_ROAD = "can.not.find.road";
	public static final String CAN_NOT_FIND_REPLENISHMENT = "can.not.find.replenishment";
	public static final String USING_ALGORITHM_ERROR = "using.algorithm.error";
	public static final String INONE_OSP_STATE="inone.osp.state.query.error";
	public static final String ITEMNO_BEGINDATE_ENDDATE_IS_NOT_EMPTY="itemNo.beginDate.endDate.is.not.empty";
	public static final String QUANTITY_GREATER_LIMIT = "quantity.greater.limit";
	public static final String SOURCE_SYSTEM_EMPTY="source.system.empty";
	public static final String BUSINESS_TYPE_EMPTY="business.type.empty";
	public static final String BILL_TYPE_EMPTY="bill.type.empty";
	public static final String BILL_NOT_EXISTS="bill.not.exists";
	public static final String BILL_STATUS_NOT_SUBMITED="bill.status.not.submited";
	public static final String BILL_STATUS_NOT_RECHECK="bill.status.not.recheck";
	public static final String BILL_STATUS_NOT_ECSS_AUDITED="bill.status.not.ecss.audited";
	public static final String CUSTOMER_CONTROL_TYPE_NOT_ALIBABA="customer.control.type.not.alibaba";
	public static final String SYS_LOOKUP_VALUES_NOT_EXISTS="syslookup.values.not.exists";
	public static final String QUERY_WHSEID_ERROR="query.whseid.error";
	public static final String BILL_SN_NOT_BOUNDED="bill.sn.not.bounded";

	public static final String QUERY_INFOR_INVENTORY_ERROR = "query.infor.inventory.error";
	public static final String QUERY_REPAIR_INVENTORY_ERROR = "query.repair.inventory.error";
	public static final String MERGE_INVENTORY_ERROR = "merge.inventory.error";
	public static final String MERGE_INVENTORY_NULL = "merge.inventory.null";
	public static final String UPLOAD_INVENTORY_ERROR = "upload.inventory.error";
	public static final String QUERY_WAREHOUSE_ERROR = "query.warehouse.error";

	public static final String DATE_NOT_EMPTY = "date.not.empty";
	public static final String HOLD_REASON_NOT_SPOT_CHECK = "hold.reason.not.spot.check";
	public static final String DETAILS_ARE_ALREADY_SUBMITTED_CANNOT_SUBMIT ="details.are.already.submitted.cannot.submit";

	public static final String DETAILS_ARE_ALREADY_SUBMITTED_CANNOT_DELETE="details.are.already.submitted.cannot.delete";

	public static final String DETAILS_IS_NULL="details.is.null";

	public static final String HAVE_SUBMITTED="have.submitted";
	public static final String TRANSFER_BILL_NOT_EXISTS="transfer.bill.not.exists";
	public static final String TRANSFER_BILL_STATUS_NOT_MAKE="transfer.bill.status.not.make";
	public static final String TRANSFER_BILL_STOCK_NOT_PROOFING="transfer.bill.stock.not.proofing";
	public static final String TRANSFER_BILL_SEND_IQC_FAILED="transfer.bill.send.iqc.failed";
	public static final String CHECK_RESULT_DETAIL_NOT_EXISTS ="check.result.detail.not.exists";
	public static final String TRANSFER_BILL_STATUS_NOT_TESTING="transfer.bill.status.not.testing";
	public static final String TRANSFER_DETAIL_STATUS_NOT_TESTING="transfer.detail.status.not.testing";
	public static final String ALGORITHM_EXECUTION_MANPOWER_INCONSISTENT = "algorithm.execution.manpower.inconsistent";
	public static final String ALGORITHM_EXECUTION_UNIT_INCONSISTENT = "algorithm.execution.unit.inconsistent";

}
