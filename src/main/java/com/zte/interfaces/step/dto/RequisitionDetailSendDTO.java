package com.zte.interfaces.step.dto;

import com.zte.interfaces.infor.dto.AliOrderSubmitDetailDTO;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Objects;

import static com.zte.common.utils.NumConstant.*;

/**
 * 推送WMS的实体对像
 */
@NoArgsConstructor
@Data
public class RequisitionDetailSendDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * PDO单号（必填）
     */
    private String pdoNo;
    /**
     * 领料单号（必填）
     */
    private String billNo;
    /**
     * 箱类别（必填）：1-原箱，0-混箱
     */
    private Integer boxType;
    /**
     * 原箱码
     */
    private String boxNo;
    /**
     * 物料代码（必填）
     */
    private String itemNo;
    /**
     * 物料条码
     */
    private String itemBarcode;
    /**
     * 批次码
     */
    private String batchBarcode;
    /**
     * 领料数量（必填）
     */
    private Integer qty;
    /**
     * 是否拆箱
     */
    private String splitBox;

    public RequisitionDetailSendDTO(AliOrderSubmitDetailDTO e) {
        this.setBillNo(e.getSrcBillNo());
        this.setBoxNo(e.getBoxNo());
        this.setItemNo(e.getItemCode());
        this.setQty(e.getQty());
        this.setPdoNo(e.getPdoNo());
        this.setItemBarcode(e.getBarcode());
        this.setBatchBarcode(e.getBatchBarcode());
        this.setBoxType(e.getBoxType());
        this.setSplitBox(e.isTransferSplit() ? STR_1:STR_0);
    }
}