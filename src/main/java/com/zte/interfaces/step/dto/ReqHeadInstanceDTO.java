package com.zte.interfaces.step.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Builder.Default;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.Map;

import static com.zte.common.utils.Constant.*;
import static com.zte.common.utils.NumConstant.STR_101;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder(toBuilder = true)
@Accessors(chain = true)
@ApiModel(description = "阿里入库单-单据头")
public class ReqHeadInstanceDTO {

    @ApiModelProperty(value = "单据号", required = false)
    private String billNo;

    @ApiModelProperty(value = "业务单据类型", required = true, example = "LXRKST")
    @NotBlank(message = "业务单据类型不能为空")
    @Default
    private String billInfoType = "LXRKST";

    @ApiModelProperty(value = "单据类型", required = true, example = "LXRK_SCCLK_NERP")
    @NotBlank(message = "单据类型不能为空")
    @Default
    private String billTypeCode = "LXRK_SCCLK_NERP";

    @ApiModelProperty(value = "出入库类型 0:入库/1:出库", required = true, example = "0")
    @NotNull(message = "出入库类型不能为空")
    @Default
    private Integer actionCode = 0;

    @ApiModelProperty(value = "状态 0030执行中等", required = true, example = "0030")
    @NotBlank(message = "状态不能为空")
    @Default
    private String statusCode = "0030";

    @ApiModelProperty(value = "来源单据号", required = true)
    @NotBlank(message = "来源单据号不能为空")
    private String srcBillNo;

    @ApiModelProperty(value = "关联单号", required = false)
    private String relatedBillNo;

    @ApiModelProperty(value = "入库库房ID", required = true)
    @NotBlank(message = "入库库房ID不能为空")
    private String toWarehouseId;

    @ApiModelProperty(value = "入库库位ID", required = true)
    @NotBlank(message = "入库库位ID不能为空")
    private String toStockId;

    @ApiModelProperty(value = "入库储位ID", required = true)
    @NotBlank(message = "入库储位ID不能为空")
    private String toLocationId;

    @ApiModelProperty(value = "申请人工号(短工号)", required = true)
    @NotBlank(message = "申请人工号不能为空")
    private String applyBy;

    @ApiModelProperty(value = "国家编码", required = true, example = "CN")
    @NotBlank(message = "国家编码不能为空")
    @Default
    private String countryCode = "CN";

    @ApiModelProperty(value = "备注", required = false)
    private String remark;

    @ApiModelProperty(value = "提交人(短工号)", required = true)
    @NotBlank(message = "提交人不能为空")
    private String submitedBy;

    @ApiModelProperty(value = "提交时间 yyyy-MM-dd HH:mm:ss", required = true)
    @NotNull(message = "提交时间不能为空")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date submitedDate;

    @ApiModelProperty(value = "创建人(短工号)", required = false)
    private String createdBy;

    @ApiModelProperty(value = "创建时间 yyyy-MM-dd HH:mm:ss", required = false)
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdDate;

    @ApiModelProperty(value = "更新人(短工号)", required = false)
    private String lastUpdatedBy;

    @ApiModelProperty(value = "更新时间 yyyy-MM-dd HH:mm:ss", required = false)
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date lastUpdatedDate;

    @ApiModelProperty(value = "接入业务系统标示", required = true, example = "101")
    @NotNull(message = "接入业务系统标示不能为空")
    @Default
    private Integer accessSystem = 101;

    @ApiModelProperty(value = "有效标示", required = true, example = "Y")
    @NotBlank(message = "有效标示不能为空")
    @Default
    private String enableFlag = "Y";

    @ApiModelProperty(value = "接入系统场景", required = true, example = "INFOR")
    @NotBlank(message = "接入系统场景不能为空")
    @Default
    private String sourceMode = "INFOR";

    @ApiModelProperty(value = "RF特性", required = false)
    private String rfFeature;

    public ReqHeadInstanceDTO(Map<String, String> configMap, String userId) {
        this.setBillInfoType(SUBMIT_BILL_TYPE);
        this.setBillTypeCode(SUBMIT_BILL_TYPE_CODE);
        this.setActionCode(0);
        this.setStatusCode(SUBMIT_BILL_STATUS_0030);
        this.setToWarehouseId(configMap.get(LOOKUP_CODE_SUBMIT_WAREHOUSE_ID));
        this.setToLocationId(configMap.get(LOOKUP_CODE_SUBMIT_LOCATION_ID));
        this.setToStockId(configMap.get(LOOKUP_CODE_SUBMIT_STOCK_ID));
        this.setCountryCode(COUNTRY_DIGIT_CODE);
        this.setApplyBy(userId);

        this.setSubmitedBy(userId);
        this.setSubmitedDate(new Date());
        this.setCreatedBy(userId);
        this.setCreatedDate(new Date());
        this.setLastUpdatedBy(userId);
        this.setLastUpdatedDate(new Date());
        this.setAccessSystem(Integer.valueOf(STR_101));
        this.setSourceMode(INFOR);
        this.setEnableFlag(FLAG_Y);
    }
}

