package com.zte.interfaces.step;

import com.zte.application.step.ECZteMeiTuanService;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.resourcewarehouse.common.annotation.ZTERestController;
import com.zte.resourcewarehouse.common.utils.BusiAssertException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

@ZTERestController("/zteMeituan")
@Api(tags = "美团数据控制器")
public class ECZteMeiTuanController {

    @Autowired
    private ECZteMeiTuanService ecZteMeiTuanService;

    @ApiOperation("库存数据上传")
    @PostMapping("/stockInfoUpload")
    public void stockInfoUpload(HttpServletRequest request, @RequestBody List<String> itemNoList) throws Exception {
        String xEmpNo = request.getHeader(Constant.X_EMP_NO);
        BusiAssertException.isEmpty(xEmpNo, MessageId.PUSH_HZBILL_TO_STORAGECENTER_001);
        ecZteMeiTuanService.uploadStockInfo(itemNoList, xEmpNo);
    }
}
