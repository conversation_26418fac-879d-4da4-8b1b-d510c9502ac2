package com.zte.interfaces.infor.dto;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

@AllArgsConstructor
@NoArgsConstructor
@Data
@Accessors(chain = true)
@Builder(toBuilder = true)
public class PendingPushAliRecordDTO {
    /**
     * 记录ID
     */
    private Long recordId;

    /**
     * 记录类型
     */
    private String recordType;

    /**
     * 事务序列号
     */
    private Long itrnSeriallKey;

    /**
     * 仓库号
     */
    private String whseId;

    /**
     * 消息ID
     */
    private String messageId;

    /**
     * 消息类型
     */
    private String messageType;

    /**
     * LPN
     */
    private String lpn;

    /**
     * LPN类型
     */
    private String lpnType;

    /**
     * sn
     */
    private String sn;

    /**
     * 数量
     */
    private BigDecimal itrnQty;
}
