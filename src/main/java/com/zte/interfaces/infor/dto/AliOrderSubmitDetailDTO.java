package com.zte.interfaces.infor.dto;

import lombok.Data;

import java.util.Objects;

import static com.zte.common.utils.NumConstant.INT_0;
import static com.zte.common.utils.NumConstant.INT_1;

/**
 * 备件改配件明细数据
 * <AUTHOR>
 * @date 2025/5/14 上午9:46
 */
@Data
public class AliOrderSubmitDetailDTO {
    /**
     * 任务号
     * */
    private String externOrderkey;
    /**
     * 单据
     * */
    private String srcBillNo;
    /**
     * 客户物料编码
     */
    private String  mpn;
    /**
     * pdo号
     */
    private String  pdoNo;
    /**
     * 序列码SN
     */
    private String barcode;
    /**
     * 无聊编码
     */
    private String itemCode;
    /**
     * 220批次码
     */
    private String batchBarcode;
    /**
     * 数量
     */
    private Integer qty;
    /**
     * 0 混箱  1 原箱
     */
    private Integer boxType;
    /**
     * 库存类型 ('pallet', 'box', 'item', 'barcode')
     */
    private String stockTypeCode;
    /**
     * 库存单元编码 (0-pallet, 1-box, 2-item, 3-barcode)
     */
    private String stockType;
    /**
     *   LPN类型 10-纸箱, 20-托盘
     */
    private String lpnType;
    /**
     * 原箱号
     */
    private String  boxNo;
    /**
     * 操作类型
     */
    private Integer operateType;
    private String sourcekey;

    /**
     * 是否原箱拆箱了
     */
    private boolean transferSplit;

    /**
     * 是否原箱
     * @return
     */
    public boolean isOriginalBox(){
        return Objects.equals(boxType, INT_1);
    }

    /**
     * 是否原箱
     * @return
     */
    public boolean isMixBox(){
        return !isOriginalBox();
    }

}
