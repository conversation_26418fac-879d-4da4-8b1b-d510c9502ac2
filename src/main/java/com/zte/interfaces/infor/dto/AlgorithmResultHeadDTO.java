package com.zte.interfaces.infor.dto;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
@Accessors(chain = true)
@Builder(toBuilder = true)
public class AlgorithmResultHeadDTO {

	private BigDecimal serialkey;
	private String taskId;
	private String algorithmScheme;
	private String expectedEffect;
	private String createdBy;
	@DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone ="GMT+8")
	private String creationDate;
	private String lastUpdatedBy;
	@DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone ="GMT+8")
	private String lastUpdateDate;
	private BigDecimal oldTotalPath;
	private BigDecimal newTotalPath;
	private BigDecimal expectedSavePath;
	private BigDecimal wholeWarehouseDistance;
	private String status;
	private String handleProgress;

}