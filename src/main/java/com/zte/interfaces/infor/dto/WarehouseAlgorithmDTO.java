package com.zte.interfaces.infor.dto;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
@Accessors(chain = true)
@Builder(toBuilder = true)
public class WarehouseAlgorithmDTO {

	private BigDecimal serialkey;
	private String whseid;
	@Excel(name = "INFOR库位",orderNum = "0",width = 15)
	private String whName;
	private String warehouse;
	@Excel(name = "库房名称",orderNum = "1",width = 15)
	private String warehouseDesc;
	private String warehouseArea;
	@Excel(name = "库区名称",orderNum = "2",width = 15)
	private String warehouseAreaDesc;
	@Excel(name = "方案类型",orderNum = "3",width = 15)
	private String schemeType;
	@Excel(name = "算法方案",orderNum = "4",width = 15)
	private String algorithmScheme;
	@Excel(name = "整库策略",orderNum = "5",width = 15)
	private String wholeWarehouseStrategy;
	@Excel(name = "最大调整库位数",orderNum = "6",width = 15)
	private BigDecimal maxWarehouseAdjust;
	@Excel(name = "单位整库工作量",orderNum = "7",width = 15)
	private BigDecimal unitWholeWarehouseWork;
	@Excel(name = "整库人力",orderNum = "8",width = 15)
	private BigDecimal wholeWarehouseManpower;
	@Excel(name = "最小整库单元",orderNum = "9",width = 15)
	private BigDecimal minWholeWarehouseUnit;
	@Excel(name = "起点X坐标",orderNum = "10",width = 15)
	private BigDecimal startCoordX;
	@Excel(name = "起点Y坐标",orderNum = "11",width = 15)
	private BigDecimal startCoordY;
	@Excel(name = "终点X坐标",orderNum = "12",width = 15)
	private BigDecimal endCoordX;
	@Excel(name = "终点Y坐标",orderNum = "13",width = 15)
	private BigDecimal endCoordY;
	@Excel(name = "执行频率(小时)",orderNum = "14",width = 15)
	private BigDecimal executionFrequency;
	private String enabledFlag;
	@Excel(name = "有效标识",orderNum = "15",width = 15)
	private String enabledFlagDesc;
	@Excel(name = "创建人",orderNum = "16",width = 15)
	private String createdBy;
	@DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone ="GMT+8")
	@Excel(name = "创建时间",orderNum = "17",width = 15)
	private String creationDate;
	@Excel(name = "最后更新人",orderNum = "18",width = 15)
	private String lastUpdatedBy;
	@DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone ="GMT+8")
	@Excel(name = "最后更新时间",orderNum = "19",width = 15)
	private String lastUpdateDate;

	private String empNo;
	private Integer pageIndex;
	private Integer pageSize;
	private Integer startRow;
	private Integer endRow;
	private String creationDateBegin;
	private String creationDateEnd;
	private List<String> algorithmSchemeList;
}