package com.zte.interfaces.infor.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;

@AllArgsConstructor
@NoArgsConstructor
@Builder(toBuilder = true)
@Accessors(chain = true)
@Data
public class PendingQualityCheckTaskDTO {
    private String type;
    private String whseid;
    private String whName;
    private String itemNo;
    private String itemBarcode;
    private String loc;
    private String lpn;
    private String lpnType;
    private String inventoryStatus;
    private String holdReason;
    private String holdReasonDsc;
    private BigDecimal qty;
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone ="GMT+8")
    private Date addDate;

    private Integer pageIndex;
    private Integer pageSize;
    private Integer startRow;
    private Integer endRow;
}
