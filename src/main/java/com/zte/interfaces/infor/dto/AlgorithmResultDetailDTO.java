package com.zte.interfaces.infor.dto;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
@Accessors(chain = true)
@Builder(toBuilder = true)
public class AlgorithmResultDetailDTO {

	private BigDecimal serialkey;
	private String taskId;
	private BigDecimal sequenceId;
	private String whseid;
	@Excel(name = "INFOR库位",orderNum = "2",width = 15)
	private String whName;
	@Excel(name = "物料代码",orderNum = "5",width = 15)
	private String sku;
	private String warehouse;
	@Excel(name = "库房名称",orderNum = "3",width = 15)
	private String warehouseDesc;
	private String warehouseArea;
	@Excel(name = "库区名称",orderNum = "4",width = 15)
	private String warehouseAreaDesc;
	@Excel(name = "算法方案",orderNum = "0",width = 15)
	private String algorithmScheme;
	@Excel(name = "LPN",orderNum = "6",width = 15)
	private String lpn;
	@Excel(name = "状态",orderNum = "7",width = 15)
	private String status;
	@Excel(name = "调整前LOC",orderNum = "8",width = 15)
	private String oldLoc;
	@Excel(name = "调整后LOC",orderNum = "9",width = 15)
	private String newLoc;
	@Excel(name = "创建人",orderNum = "10",width = 15)
	private String createdBy;
	@DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone ="GMT+8")
	@Excel(name = "创建时间",orderNum = "11",width = 15)
	private String creationDate;
	private String lastUpdatedBy;
	@DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone ="GMT+8")
	private String lastUpdateDate;
	@Excel(name = "任务号",orderNum = "1",width = 15)
	private String personId;

	private String empNo;
	private Integer pageIndex;
	private Integer pageSize;
	private Integer startRow;
	private Integer endRow;
	private String creationDateBegin;
	private String creationDateEnd;
}