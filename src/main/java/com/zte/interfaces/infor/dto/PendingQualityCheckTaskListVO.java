package com.zte.interfaces.infor.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;

@AllArgsConstructor
@NoArgsConstructor
@Data
@Accessors(chain = true)
@Builder(toBuilder = true)
public class PendingQualityCheckTaskListVO {
    private Integer pageIndex;
    private Integer total;
    private Integer pageCount;
    private List<PendingQualityCheckTaskDTO> pendingQualityCheckTaskDTOList;
}
