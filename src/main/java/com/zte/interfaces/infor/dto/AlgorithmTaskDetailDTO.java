package com.zte.interfaces.infor.dto;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
@Accessors(chain = true)
@Builder(toBuilder = true)
public class AlgorithmTaskDetailDTO {

	private String taskId;
	private String algorithmScheme;
	@DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone ="GMT+8")
	private String creationDate;
	private String personId;
	private String whseid;
	private String whName;
	private String status;
	private String handleProgress;
	private String sku;

	private String empNo;
	private Integer pageIndex;
	private Integer pageSize;
	private Integer startRow;
	private Integer endRow;
}