package com.zte.interfaces.infor;

import com.zte.application.infor.PendingPushAliRecordService;
import com.zte.interfaces.infor.dto.PendingQualityCheckTaskDTO;
import com.zte.interfaces.infor.dto.PendingQualityCheckTaskListVO;
import com.zte.resourcewarehouse.common.annotation.ZTERestController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

/**
 * 待推送阿里记录控制器
 */
@ZTERestController("/pendingPushAliRecord")
@Api(tags = "待质量判定库推送记录", description = "PendingPushAliRecordController")
public class PendingPushAliRecordController {

    @Autowired
    private PendingPushAliRecordService pendingPushAliRecordService;

    @ApiOperation("查询待质量判定库任务")
    @PostMapping("/getPendingQualityCheckTaskList")
    public PendingQualityCheckTaskListVO getPendingQualityCheckTaskList(HttpServletRequest request, @RequestBody PendingQualityCheckTaskDTO dto) {
        return pendingPushAliRecordService.getPendingQualityCheckTaskList(dto);
    }
}
