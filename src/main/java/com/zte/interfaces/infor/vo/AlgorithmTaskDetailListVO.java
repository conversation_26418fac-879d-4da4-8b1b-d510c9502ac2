package com.zte.interfaces.infor.vo;

import com.zte.interfaces.infor.dto.AlgorithmResultDetailDTO;
import com.zte.interfaces.infor.dto.AlgorithmTaskDetailDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
@Accessors(chain = true)
@Builder(toBuilder = true)
public class AlgorithmTaskDetailListVO {

     private Integer pageIndex;
     private Integer total;
     private Integer pageCount;
     private List<AlgorithmTaskDetailDTO> algorithmTaskDetailDtos;

}
