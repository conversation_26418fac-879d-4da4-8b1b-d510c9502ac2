RetCode.Success=Success
RetCode.ServerError=Server Error
RetCode.AuthFailed=Auth Failed
RetCode.PermissionDenied=Permission Denied
RetCode.ValidationError=Validation Error
RetCode.BusinessError=Business Error
date.formate.error = date formate error
whseid.can.not.be.empty=whseid can not be empty
invoke.success=invoke success
invoke.failed=invoke failed
updata.success=updata success
no.data.found=no data found
sync.eccn.lookupcode.not.config= sync eccn lookupcode not config
sku.reelid.wmwhse.is.not.empty=whseid can not be empty
sku.reelid.wmwhse.is.not.val1=The warehouse does not meet the rules, for example WMWHSE1
sku.reelid.wmwhse.is.not.val2=Warehouse can only input WMWHSE1-WMWHSE50
sku.reelid.sku.is.not.empty=Item code cannot be empty
sku.reelid.sku.is.not.val1=Item code cannot exceed

infor.fallback.price.no.data=The data does not exist, or it is not a return order
infor.fallback.price.fallback.no.empty=Return order No. cannot be blank
infor.fallback.price.fallback.detail=Details cannot be blank
infor.fallback.price.detail.not.val1=The primary key of row {0} cannot be empty
infor.fallback.price.detail.not.val2=The outer line of line {0} cannot be empty
infor.fallback.price.detail.not.val3=The bar code in line {0} cannot be empty
infor.fallback.price.detail.not.val4=The number of rows {0} cannot be less than or equal to 0
infor.fallback.price.detail.not.val5=Currency in line {0} cannot be blank
infor.fallback.price.detail.not.val6=The price in line {0} cannot be less than or equal to 0
infor.fallback.price.detail.not.val7=Tax free price in line {0} cannot be less than or equal to 0
infor.fallback.price.detail.not.val8=Unit price in line {0} cannot be less than or equal to 0
infor.fallback.price.detail.not.val9=Unit price excluding tax in line {0} cannot be less than or equal to 0
infor.fallback.price.detail.not.val10=The exchange rate in line {0} cannot be less than or equal to 0
infor.fallback.price.detail.not.val11=Tax rate of line {0} cannot be less than or equal to 0
infor.fallback.price.is.exist=Price is not allowed to be modified
infor.fallback.price.no.sample.with.infor=The data is inconsistent with infor, please check
infor.fallback.price.query.no.data=No data can be found, please check
infor.fallback.price.data.can.not.sample=The quantity is inconsistent, please check
infor.fallback.applybill.not.exist=The fallback applybill not exist
infor.fallback.syslookup.values.not.exist=The sysLookup values 1000034 not exist

infor.vmi.price.billNo.can.not.empty = The billNo cannot be empty
infor.vmi.price.serailKey.can.not.empty = The serailKey cannot be empty
infor.vmi.price.exlineNo.can.not.empty = The exlineNo cannot be empty
infor.vmi.price.itemBarcode.can.not.empty = The itemBarcode cannot be empty
infor.vmi.price.currencyType.can.not.empty = The currencyType of cannot be empty
infor.vmi.price.uniPrice.can.not.empty = The uniPrice cannot be less than or equal to 0
infor.vmi.price.uniPriceNoTax.can.not.empty = The uniPriceNoTax cannot be less than or equal to 0
infor.vmi.price.price.can.not.empty = The price cannot be less than or equal to 0
infor.vmi.price.priceNoTax.can.not.empty = The priceNoTax cannot be less than or equal to 0
infor.vmi.price.exchangeRate.can.not.empty = The exchangeRate cannot be less than or equal to 0
infor.vmi.price.taxRate.can.not.empty = The taxRate cannot be less than or equal to 0
infor.vmi.price.has.updated = {0} the price has been refreshed and cannot be refreshed again
infor.vmi.price.not.existed = {0} not existed
infor.vmi.price.data.can.not.empty = The data is empty

infor.dispense.serial.whseid.val=Warehouse not activated
infor.dispense.serial.receipt=Received data has been generated, inserting is prohibited

Nonconforming.info.001 = The barcodes on the unqualified-product list all come from the old system.
Nonconforming.info.002 = barCode fallback Qty From Isrm large to this Nonconforming Bill !
Nonconforming.info.003 = The Nonconforming Bill has no itembarcode to return!
Nonconforming.info.004 = The list of unqualified products does not exist!
Nonconforming.info.005 = The status of the unqualified-product list is incorrect!
Nonconforming.info.005 = The unqualified product list has not been entered by the material technology manager. Please confirm.
infor.no.wait.feed.applyNo = infor no wait feed apply No
pushHZBillToStorageCenter.info.001 = the token value or emptNo can not be empty
barcode.center.validation.registerBarcode.empty=Register barcode and container/batch barcode cannot be empty at the same time
barcode.center.validation.response.format=Barcode center response data format is incorrect


infor.vmi.invquery.itemNo.cannot.empty = the itemNo cannot be empty
infor.vmi.invquery.itemNo.supplyNo.must.enter.one=must enter a supplyNo or an itemNo
infor.so.sodetail.query.cannot.empty=Must Enter an Param
infor.so.sodetail.query.error.dateformat=Error DateFormat
infor.so.edisosdetail.query.cannot.empty=the externalorderkey2 cannot be empty
infor.controller.error.dateformat = Error DateFormat


enter.at.least.one.query.condition= Enter at least one query condition.
dropid.and.produceno.not.empty = dropid can not be empty
input.data.can.not.null = can not be null
can.not.be.greater.than.five.hundred = can not be greater than five hundred
this.method.had.been.running = this method had been running
can.not.get.key.value = can not get key value
please.input.key.value= please input key value
data.update.error = data update error

recheckno.can.not.be.empty = recheckno can not be empty
recheckno.not.exists = recheckno not exists
recheckno.not.need.to.check = recheckno not need to check
recheckno.no.details = recheckno no details
sys.lookup.values.1000037.not.exist = sys lookup values 1000037 not exist

call.back.data.empty = call back data is empty
bill.no.empty = bill no is empty
oms.sale.bill.not.exists = oms sale bill not exists
iwms.qcbad.return.apply.bill.not.exists = iwms qcbad return apply bill not exists
oms.sale.ecss.lookup.not.exists = oms sale bill sys lookup values not exists
can.not.be.greater.than.fifty = can not be greater than fifty, please confirm
hold.reason.not.sample.hold = The reason for freezing is not due to sample over sampling or inventory sampling. Please confirm
inventory.hold.record.exists = inventory hold record exists, please confirm
item.barcode.not.exists = item barcode is not exists, please confirm
ba.item.not.exists = ba item is not exists, please confirm
ba.bom.head.not.exists = ba bom head is not exists, please confirm
choose.inventory.hold.record.effected = choose inventory hold record is effected, please confirm
choose.inventory.hold.record.expired = choose inventory hold record is expired, please confirm
please.login.first= please login first
remark.too.long = remark can not long than 60
can.not.be.greater.than.five.thousand = can not be greater than five thousand
sku.domestic.internal.enable.update.by.empty = sku/domesticFirst/internalFirst/enableFlag/updateBy can not be empty
sku.can.not.repeat = sku can not repeat
stockNo.itemBarcode.check.count=STEP location and material barcode support up to 500
no.params=no params
stockNo.check=No INFOR storage location found
itembarcode.repeat.check=220 barcode has duplicate data
require.repeat.import = require.repeat.import
require.repeat.factory.item = require.repeat.factory.item
require.item.not.exists = require.item.not.exists
factory.sku.domestic.qty.empty = factory.sku.domestic.qty.empty
require.item.length.fail = require.item.length.fail
check.inforloc=The information storage location code does not exist
push.b2b.failed=push b2b failed
get.order.query.for.infor.failed  = post isrm orderQueryForInfor failed
get.return.order.query.for.infor.failed  = post isrm returnOrderQueryForInfor failed
inventory.hold.approvedby.empty = inventory.hold.approvedby.empty
failed_to_process_approval_center_kafka_message = failed_to_process_approval_center_kafka_message
item.qte.stqe.not.exists = item.qte.stqe.not.exists
iqc.test.requisition.detail.not.val1 = iqc.test.requisition.detail.not.val1
iqc.test.requisition.detail.not.val2 = iqc.test.requisition.detail.not.val2
item.no.not.exists = item no is not exists, please confirm
iqc.test.requisition.reel.not.null = iqc.test.requisition.reel.not.null
iqc.test.requisition.stock.error = iqc.test.requisition.stock.error
iqc.test.requisition.query.param.is.null = iqc.test.requisition.query.param.is.null
infor.loc.not.exists= loc not exists, please confirm
warehouse.road.work.exists = warehouse road work exists, please confirm
warehouse.road.work.repeated = warehouse road work repeated, please confirm
warehouse.algorithm.exists = warehouse algorithm exists, please confirm
warehouse.algorithm.repeated = warehouse algorithm repeated, please confirm
algorithm.scheme.type.inconsistent.exists = scheme type is inconsistent with the maintained algorithm scheme
algorithm.whole.warehouse.strategy.inconsistent.exists = whole warehouse strategy is inconsistent with the maintained algorithm scheme
algorithm.max.warehouse.adjust.inconsistent.exists = max warehouse adjust is inconsistent with the maintained algorithm scheme
algorithm.unit.whole.warehouse.work.inconsistent.exists = unit whole warehouse work is inconsistent with the maintained algorithm scheme
algorithm.start.coord.x.inconsistent.exists = start coord x is inconsistent with the maintained algorithm scheme
algorithm.start.coord.y.inconsistent.exists = start coord y is inconsistent with the maintained algorithm scheme
algorithm.end.coord.x.inconsistent.exists = end coord x is inconsistent with the maintained algorithm scheme
algorithm.end.coord.y.inconsistent.exists = end coord y is inconsistent with the maintained algorithm scheme
algorithm.execution.frequency.inconsistent.exists = execution frequency is inconsistent with the maintained algorithm scheme
algorithm.scheme.type.inconsistent = The imported algorithm scheme type is different
algorithm.whole.warehouse.strategy.inconsistent = The imported algorithm whole warehouse strategy is different
algorithm.max.warehouse.adjust.inconsistent = The imported algorithm max warehouse adjust is different
algorithm.unit.whole.warehouse.work.inconsistent = The imported algorithm unit whole warehouse work is different
algorithm.start.coord.x.inconsistent = The imported algorithm start coord x is different
algorithm.start.coord.y.inconsistent = The imported algorithm start coord y is different
algorithm.end.coord.x.inconsistent = The imported algorithm end coord x is different
algorithm.end.coord.y.inconsistent = The imported algorithm end coord x is different
algorithm.execution.frequency.inconsistent = The imported algorithm execution frequency is different
delivery.no.received.not.exists = delivery.no.received.not.exists
delivery.no.received.repeat = delivery.no.received.repeat
productbase.no.data.whse = productbase.no.data.whse
transfer.stock.receive.for.infor.failed = post isrm transferStockReceiveForInfor failed
eccn.version.eccn.for.iwms.failed = post eccn versionEccn failed
red.dot.status.check = The red dot status of INFOR outbound task has been updated!
red.dot.execute.date.check = Execution interval less than {0} minutes!
msl.wmwhse.is.not.empty = The warehouse cannot be empty
msl.sku.is.not.empty = Material code cannot be empty
msl.lottable02.is.not.empty = Material barcode cannot be empty
msl.serialnumber.is.not.empty = Reelid cannot be empty
inone.osp.state.query.error = Osp state query error
can.not.find.road = The corresponding road network information cannot be found in the current query conditions.
can.not.find.replenishment = No replenishment information can be found in the current query conditions.
using.algorithm.error = The interface that invokes the algorithm is incorrect.
itemNo.beginDate.endDate.is.not.empty = itemNo, beginDate, endDate is not empty.
quantity.greater.limit = The quantity of itemNo is greater than the maximum permitted limit.
source.system.empty = source system cannot be empty
business.type.empty = business type cannot be empty
bill.type.empty = bill type cannot be empty
bill.not.exists = bill not exists
bill.status.not.submited = bill not submited
bill.status.not.recheck = bill not recheck
bill.status.not.ecss.audited = bill not ecss audited
customer.control.type.not.alibaba = customer control type not alibaba
syslookup.values.not.exists = syslookup values not exists
query.whseid.error = query infor whseid error
bill.sn.not.bounded = bill sn not bounded
query.infor.inventory.error = Infor Inventory query data abnormality
query.repair.inventory.error = Repair Inventory query data abnormality
merge.inventory.error = Inventory summary data abnormality
merge.inventory.null = Inventory summary data is null
upload.inventory.error = Inventory upload data abnormality
query.warehouse.error = Query warehouse info error
date.not.empty = date is not empty
hold.reason.not.spot.check = The reason for freezing is not due to inventory sampling, please confirm
details.are.already.submitted.cannot.submit=details are already submitted,cannot submit
details.are.already.submitted.cannot.delete=details are already submitted,cannot delete
details.is.null=details is null
have.submitted=MPN{0}have submitted
params.limit=More than 500 entries are allowed.
transfer.bill.not.exists=transfer bill not exists
transfer.bill.status.not.make=transfer bill status not make
transfer.bill.stock.not.proofing=transfer bill stock not proofing
transfer.bill.send.iqc.failed=transfer bill send iqc failed
check.result.detail.not.exists=check result detail not exists
transfer.bill.status.not.testing=transfer bill status not testing
transfer.detail.status.not.testing=transfer detail status not testing
algorithm.execution.manpower.inconsistent=Inconsistency in manpower across the entire library for importing algorithm schemes
algorithm.execution.unit.inconsistent=The minimum integer library unit for importing the algorithm scheme is inconsistent