######################### MSB注册信息配置 ##################################
servicecenter = msb
server.port = 8080
eureka.client.enabled = false
## msb地址
register.address = msrtest.zte.com.cn:10081
## msb的nginx vip,端口
register.node.ipport = imes.dev.zte.com.cn:80
##本地启动服务默认不注册MSB,如有需要请另行开启
#register.enable=false
register.serviceName = ${spring.application.name}

######################### swgger配置 ##################################
## 打开/关闭swgger，生产环境需配置为false屏蔽swagger
#springfox.documentation.enabled = false
#springdoc.api-docs.enabled = false
#springfox.documentation.auto-startup = false
#springfox.documentation.swagger-ui.enabled = false

######################### 加密配置 ##################################
## 用于数据库账号密码加解密
msa.rootkey.factor1 = CTtV3kfCc3AzGBYu2uat
msa.rootkey.salt = MIGszG6VhHv57AB7iXqD
msa.encrypt.datakey = hd911LC1xBfwOqjKVupmMZbS+mYlfZGMSG/bJq6FIg13LMc1n4fhunHRkp8CksN0sJkbI7wjZId5x8UNghSFyQ==
msa.ccs.encrypt.factor = KENC(Qs/wqc/JgUHg54bqXmg5lT2xkOeipCnWiuaAGNnaJsGbJy0Tg0YpJg==$$z1uW2fnWq97IfDNk)
###########KMS配置###########
msa.security.server.dt-kms.enable = true
msa.security.server.dt-kms.appName = ENC(o3LVMlUQGtmNUCFZryl96sgxwxMd32QaCgW7Is/l1Z8b2Oy+jJPi8db7/Ebk)
msa.security.server.dt-kms.appSecret = ENC(uD9JI1pz4Zi33G80qmkq4Dyz4BKZvho6RjEi4m0Qcon/nc6+8zf6/VK5aLyY7PHHdlUZcKUnDt3Aq1oUK7WXHKKG)
msa.security.server.dt-kms.userId = ENC(4TY6Jq5A/BAAQP0BR5XtqjUHaPyNOVwCqEYHiC0HQ7dh7lJPh2ywhoW465I=)
msa.security.server.dt-kms.endpoint = kms-msp.test.zte.com.cn
## = ZteSecurity的加密服务端类型，可选值：isoa-security、dt-kms，默认值isoa-security
msa.security.client.zteSecurity.serverType = dt-kms
### = 当加密服务端为dt-kms，该配置设置为true时，zteSecurity默认会使用kms服务端进行解密，若解密失败尝试使用isoa-security服务端进行解密
msa.security.client.zteSecurity.decryptCompatibleEnable = true
#### = 配置默认使用的主密钥标识，只有一个时可省略
msa.security.server.dt-kms.defaultMasterKey = inforKeys
msa.security.server.dt-kms.masterKeys.inforKeys.secretKeyType = xyiscp:imes300:inforweb:general
##### = 配置主密钥标识inforKeys默认使用的数据密钥标识，只有一个时可省略
msa.security.server.dt-kms.masterKeys.inforKeys.defaultDataKey = general
msa.security.server.dt-kms.masterKeys.inforKeys.dataKeys.general = M2EyZGJkZDA4NmVjMGE0MDA0ZDg2YmY5NzQyOTc3Mzk2N2E3NDAxZDE4MGM1ZjM3ZmU3ZjM3OTliMjI5MmJiNDI3OGMyODgxYmU0NTUwMDRiYjVhY2UyYzJlMTg2ZmI2$$NjRlOGI3ZDVmNDI4OTA1OTgyY2ZmZWYwYmIzMmYxZGE=&&YWFkZDZkZjktZWY4MC00ZGFjLWJmMDAtMWU1OTAzZDBiYTA4

######################### 数据库配置 ##################################
## iWMS数据库
jdbc1.driverClassName = com.mysql.jdbc.Driver
jdbc1.type = com.alibaba.druid.pool.DruidDataSource
jdbc1.url = ********************************************************************************************************************************************************************************************************************************************************************************************************************************************************
jdbc1.username = KENC(Ta9qFYsw/tz4JiBh5V2uEq4IBvTtBA==$$zEjNOl7w+vqmLzfv)
jdbc1.password = KENC(RZhOURPKUCoZWGo56t6Ndnfp5H3EckQMXFpELQ==$$kTaSjcpzUcLtcqVP)
## infor数据库
jdbc2.driverClassName = oracle.jdbc.driver.OracleDriver
jdbc2.type = com.alibaba.druid.pool.DruidDataSource
jdbc2.url = ********************************************
jdbc2.username = KENC(/oImQFGU3WRgLBN/B957UXyqgFygyg==$$uKgScGDqqXcirjwm)
jdbc2.password = KENC(fQIZoA0EuPOX2dR3Heu/L6jpCfp3MUJdTwE=$$EuVP8J2qLQmIkrGB)
## step数据库
step.driverClassName = oracle.jdbc.driver.OracleDriver
step.type = com.alibaba.druid.pool.DruidDataSource
step.url = **********************************************
step.username = KENC(LCx4ts6Turaq4MF2VQKgx34ymt14H3vK9Q==$$iKcBR9MGtX25XT3T)
step.password = KENC(geCTRouZTfG3iDUKb9pY4gIzjb48HSNVUywn0Spd$$09NzxKqw2Z+Ln0fL)

######################### python配置 ##################################
python.filepath = /data/wireless4.5
python.command = /data/wireless4.5/myRun.py
python.inputfile = /data/wireless4.5/testfile.json

######################### upp配置 ##################################
upp.active = false
## 当前产品id, 向统一权限系统索要（区分环境）
upp.auth.productId = 3813
## 当前模块id, 由DT管理员创建（区分环境）
upp.auth.moduleId = 443363
## 租户id,测试环境:4304    生产环境:2
upp.auth.tenantId = 4304
## 产品密钥key，向统一权限索要（区分环境）
upp.auth.productSecretKey = KENC(0KP2ECfemRoS5fF6H6rTf+QtZ4MYg2gY9EPVYHdvdvpAv4RNfuCTZA7DoXbFHaJm$$7Zm+n2D4uNOBeM2G)
## 鉴权地址（区分环境）#测试环境：https://uactest.zte.com.cn/zte-sec-upp-authbff；生产环境：https://uac.zte.com.cn/zte-sec-upp-authbff；仿真环境：https://uactest.zte.com.cn:8081/zte-sec-upp-authbff
upp.auth.authUrl = https://uactest.zte.com.cn/zte-sec-upp-authbff
## 授权地址（区分环境）#测试环境：https://uactest.zte.com.cn/zte-sec-upp-bff；生产环境：https://uac.zte.com.cn/zte-sec-upp-bff；仿真环境：https://uactest.zte.com.cn:8081/zte-sec-upp-bff
upp.auth.manageUrl = https://uactest.zte.com.cn/zte-sec-upp-bff
## 数据对称加密密钥
upp.auth.encryptor = KENC(uiKVTV0cAA1JUSHqnzDvrBFWHbI3qKS7M0eGPg77LjhB5xwgpk3bqnK/kz3mhE9QmePx2knCPWIKSJNghx+MDTo+YBBnvhkLJICUPGXjOXYZrrNNHgR4BycjIzVFqebGCKuFsJ7BnHO65XcUfMr9EJ/N267bxMlUtjqpEKMhoJ9bemnD97plPh/wbTFRbxXFUwQX0AoqgJPw4buAsKblO+sAkYq/9rXU2dcraNw2S/06mPIY3sIQuTtY1DI2LIzX0whmuzWNJiDg3ecROCSs4y07S/dg9SveZk3fX16eDqjxGjgaTL2t7A==$$IlYW7JWzDhJUwr04)
## 签名验证，sha256加密盐值
upp.sha256.salt = KENC(8Q8GmgFGI0mDLFmhDJT2P2sXpy7eIIMjGKKrwLKLyGvIxuV+mJsLQV8Lh7g8lhJoV71VrSVRCaretgZU21Qkrb0voyRwfNUTH7/oROtKaPGzPQbFPbSxn3cbY5YVtu4R3lHQWbf3BcHop7OT6S6j6sQ/aNME6lUmoUWdCwm0pAGb1mfGYTqp3w1KUX6MaseO$$gZT/92QWTrglQCJo)

######################### 消息云配置 ##################################
message.inoneAppCode = KENC(rB/HuL3UFMzn9cLee9xvurRCe7McVOVo18COrNVXU96Tl1dGQ5XaMhokQmAvy83g$$LLGUpMrHhVeH5VeV)
message.server = https://icosg.test.zte.com.cn/zte-itp-msp-message/v1

######################### 缓存云配置 ##################################
redis.mode = 4
msa.redis.serializerType = genericJackson2Json
msa.ccs.enable = true
msa.ccs.resourceCode = mestest
cacheCloud.server = http://uat.sz-cache.zcloud.zte.com.cn:8888/zte-itp-msp-cachecloud/

######################### 文档云配置 ##################################
cloudDiskSDK.host = https://idrive.test.zte.com.cn/zte-km-cloududm-clouddisk
cloudDiskSDK.httpClientLogSwitch = true
cloudDiskSDK.xOrgId = MES_ACCESS_KEY
cloudDiskSDK.xOriginServiceName = MES
cloudDiskSDK.xSecretKey = KENC(oCUiYWg918S6Bwg4jdMozsCa4uAfk4tl$$vH4abaoNjWGFXvye)

######################### EMS配置 ##################################
## 告警上报地址  必填：测试：https://ume.test.zte.com.cn，仿真：https://ume.uat.zte.com.cn，生产：https://ume.zte.com.cn
dtems.agent.amosBaseUrl = https://ume.test.zte.com.cn
## 组件启用总开关 非必填，默认为true
dtems.agent.enable = true
## 应用实例ID，必填
dtems.agent.applicationInsId = 8819771085188103
## 业务ID，必填（业务名称：SPM告警数据上报NOC）
dtems.agent.businessId = 673208954151534592
## 告警码：原材料仓储STEP核心JOB异常告警
dtems.agent.alarmCode.step.job.monitor = 1102554557


######################### 网关鉴权 ##################################
common.auth.enabled = false
common.auth.secretKey = KENC(ziwpT1IKN0FEpzl3OqrVuJGtsD0QDDzrGskFZz0Jhxx3ZiBhff3JJ3dFQrTvKiou8ACzxQ==$$xmsWT7hZPBlFmwwN)
common.auth.ignore.emp = ***********
common.auth.ignore.path = /info
common.auth.ignore.ip = 10.118.22.1851
common.auth.timeDiff = 60


######################### 外部系统配置 ##################################
## imes
imes.centerFactory.url = https://imes.dev.zte.com.cn
imes.url = https://imes.dev.zte.com.cn/zte-mes-manufactureshare-gateway
imes.access.key = KENC(5NkStslqpmspPk8EPhRtqrk9aK6gT0ePucekX7V7wv4E36qJEGabLRj2tuJv80Nn$$YCWjU8s/kU2Wkjw2)
imes.access.secret = KENC(gisi+Pyx1G57CgFOhjJxNZTXHTz5GiMwDqt6s4Hp57LBcmSwAWXwLq96xqRiTnfkQoQ4YgmV3qEzv15CC4673dmQPiW6G6GufHwZEZXqPiw=$$iHmSAYTuyioys30o)
imes.secret = KENC(v5XzqA4V5ExR+/MGQRXoFSKy4ECJqeNsrbyTpbBSCYE=$$ENlJlCdWzXQsIoo1)
## iwms
iwms.url = http://test.iwms.zte.com.cn:8888
iwms.auth.value = KENC(7Fc1OsYOI+dz+dDjTjY9y2Woc5RJeQJY+ERKbBkxQM8CJ7qhWKO+eTAD2U8ADp0tzTJjqp9nlmhuNzfsmg==$$gVa5DvJKrPdJfu+F)
## eccn
eccn.token = KENC(PBOue51CNAZ/RNID7YjtRiwGJK1NDaHeZ9JbgmuRlx7Ab/OaG4bc96oXhIJo/bg3$$lQEit29l3jpJfAqv)
## ecss
ecss.url = https://zecss.uat.zte.com.cn:8443
ecss.iwms.auth.value = KENC(oqmA2fP4b6R3C/vr8jT7skB0fYqFTVRkRYNuKK8C5kw=$$q5bBv44aKhCyZHfw)
ecss.iwms.feederSystem.token = KENC(XE8649Yy4cE0iRG3TtNKzhvd8CeFptvdWSFk2wp07JckC4X3r9vo6Xa0wcjrM6ac842IM4xi5H/VL8Wuuy9vgB1l0v+s2rtmAEOZLdmQTZM=$$UA0JV5hd1GY9tis/)
## 移动审批
approval.sdk.app.appId = APPE3331693618155958
approval.sdk.app.secretKey = KENC(5q0Yn/jiwEyKO0zISRBk6DEuYSAmmerePcE5FHtasn3JGPVEeVVJywVc2zF1RtwsK0vi5RJvDIBtoNo2qwnUMyk46dbjqhRREHhqfzhjd6Y=$$beQ7zl7kEZMAkZpL)
approval.sdk.app.appCode = OMS
approval.sdk.header.xEmpNo.default = 10201202
## inone
# in.one.url = https://icosg.test.zte.com.cn
in.one.url = http://imes.dev.zte.com.cn
in.one.wms.app.code = KENC(rB/HuL3UFMzn9cLee9xvurRCe7McVOVo18COrNVXU96Tl1dGQ5XaMhokQmAvy83g$$LLGUpMrHhVeH5VeV)
## b2b
b2b.header.appId = internal_iMES
b2b.authorization.appSecret = KENC(m15I2WBlC2ga3IG655ZF/oADTosPNVvlCVWcfqSPJ8R4tKVGFfyMTusaai5F3d5A1FnXsA==$$SM+/byzn5B8dJCF3)
b2b.url = https://b2b.test.zte.com.cn/gateway/common/1.0/api/receive
## 评审中心
review.appId = 504249875830
review.flow.temp.code = RP241118003
review.flow.temp.code.detail = RP241211005
## 冻结失败触发红点任务
redDot.task.url = https://momc.zx.zte.com.cn/api/ReddotTask/AddReddotTask
redDot.task.token = KENC(0MsySzsUke+y+5+mvYyUejp9zd3Ie+n6QXHTKbRJmJWjxH/H0PGAr7X49YR192f0FJzZzQ==$$S5kod80GKPTtJQMN)
