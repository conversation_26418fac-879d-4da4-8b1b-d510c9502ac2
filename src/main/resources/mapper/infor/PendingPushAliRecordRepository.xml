<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.domain.model.infor.PendingPushAliRecordRepository">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.zte.domain.model.infor.PendingPushAliRecord">
        <id column="RECORD_ID" property="recordId" jdbcType="BIGINT"/>
        <result column="RECORD_TYPE" property="recordType" jdbcType="NVARCHAR"/>
        <result column="ITRN_SERIALKEY" property="itrnSeriallKey" jdbcType="BIGINT"/>
        <result column="WHSEID" property="whseId" jdbcType="NVARCHAR"/>
        <result column="MESSAGE_ID" property="messageId" jdbcType="NVARCHAR"/>
        <result column="MESSAGE_TYPE" property="messageType" jdbcType="NVARCHAR"/>
        <result column="SEND_STATUS" property="sendStatus" jdbcType="VARCHAR"/>
        <result column="SEND_TIMES" property="sendTimes" jdbcType="INTEGER"/>
        <result column="ADDDATE" property="addDate" jdbcType="TIMESTAMP"/>
        <result column="ADDWHO" property="addWho" jdbcType="NVARCHAR"/>
        <result column="EDITDATE" property="editDate" jdbcType="TIMESTAMP"/>
        <result column="EDITWHO" property="editWho" jdbcType="NVARCHAR"/>
        <result column="ENABLED_FLAG" property="enabledFlag" jdbcType="NVARCHAR"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        RECORD_ID, RECORD_TYPE, ITRN_SERIALKEY, WHSEID,
        MESSAGE_ID, MESSAGE_TYPE, SEND_STATUS, SEND_TIMES, ADDDATE, ADDWHO, EDITDATE, EDITWHO, ENABLED_FLAG
    </sql>

    <select id="getPendingQualityCheckTaskListTotal" parameterType="list" resultType="java.lang.Integer">
        select count(1)
        from (<foreach collection="zteWarehouseList" item="inventoryDb" index="index" separator="union all">
        select 1
        from ${inventoryDb.warehouseId}.lotxlocxid lx
        join ${inventoryDb.warehouseId}.lotattribute lt
        on lx.lot = lt.lot
        and lx.storerkey = lt.storerkey
        join ${inventoryDb.warehouseId}.loc lc
        on lc.loc = lx.loc
        join plugin.inventoryhold_record t
        on t.whseid = lx.whseid
        and t.item_no = lx.sku
        and t.item_barcode = lt.lottable02
        and t.hold_status = 1
        join plugin.sys_lookup_values b
        on b.lookup_meaning = t.hold_reason
        and b.lookup_type = '1000037'
        and b.enabled_flag = 'Y'
        and b.attribute1 = '质量冻结'
        where lx.storerkey = 'ZTE'
        and lx.qty > 0
        and lc.locationtype != 'PENDINGQC'
        and exists (select 1
        from ${inventoryDb.warehouseId}.sku sk
        where sk.sku = t.item_no
        and sk.storerkey = 'ZTE'
        and sk.customer_control_type = 1)
        union all
        select 1
        from ${inventoryDb.warehouseId}.lotxlocxid lx
        join ${inventoryDb.warehouseId}.lotattribute lt
        on lx.lot = lt.lot
        and lx.storerkey = lt.storerkey
        join ${inventoryDb.warehouseId}.loc lc
        on lc.loc = lx.loc
        join plugin.inventoryhold_record t
        on t.whseid = lx.whseid
        and t.item_no = lx.sku
        and t.item_barcode = lt.lottable02
        and t.hold_status = 0 --失效
        join plugin.sys_lookup_values b
        on b.lookup_meaning = t.hold_reason
        and b.lookup_type = '1000037'
        and b.enabled_flag = 'Y'
        and b.attribute1 = '质量冻结'
        where lx.storerkey = 'ZTE'
        and lx.qty > 0
        and lc.locationtype = 'PENDINGQC'
        and exists (select 1
        from ${inventoryDb.warehouseId}.sku sk
        where sk.sku = t.item_no
        and sk.storerkey = 'ZTE'
        and sk.customer_control_type = 1)
    </foreach>)
    </select>

    <select id="getPendingQualityCheckTaskList" resultType="com.zte.interfaces.infor.dto.PendingQualityCheckTaskDTO">
        select * from (select rownum as rn, s.*
        from (<foreach collection="zteWarehouseList" item="inventoryDb" index="index" separator="union all">
        select '待质量判定库入' as type,
        t.whseid,
        (select pd.db_alias
        from wmsadmin.pl_db pd
        where upper(pd.db_logid) = #{inventoryDb.warehouseId}) as wh_name,
        lx.sku as item_no,
        lt.lottable02 as item_barcode,
        lx.loc,
        lx.id as lpn,
        decode((select 1
        from plugin.zms_original_box_info zo
        where zo.original_box_id = lx.id
        and rownum = 1),
        '1',
        '原箱',
        '混箱') as lpn_type,
        lx.status as inventory_status,
        t.hold_reason,
        b.lookup_meaning as hold_Reason_Dsc,
        lx.qty,
        t.ADDDATE
        from ${inventoryDb.warehouseId}.lotxlocxid lx
        join ${inventoryDb.warehouseId}.lotattribute lt
        on lx.lot = lt.lot
        and lx.storerkey = lt.storerkey
        join ${inventoryDb.warehouseId}.loc lc
        on lc.loc = lx.loc
        join plugin.inventoryhold_record t
        on t.whseid = lx.whseid
        and t.item_no = lx.sku
        and t.item_barcode = lt.lottable02
        and t.hold_status = 1
        join plugin.sys_lookup_values b
        on b.lookup_meaning = t.hold_reason
        and b.lookup_type = '1000037'
        and b.enabled_flag = 'Y'
        and b.attribute1 = '质量冻结'
        where lx.storerkey = 'ZTE'
        and lx.qty > 0
        and lc.locationtype != 'PENDINGQC'
        and exists (select 1
        from ${inventoryDb.warehouseId}.sku sk
        where sk.sku = t.item_no
        and sk.storerkey = 'ZTE'
        and sk.customer_control_type = 1)

        union all

        select '待质量判定库出' type,
        t.whseid,
        (select pd.db_alias
        from wmsadmin.pl_db pd
        where upper(pd.db_logid) = #{inventoryDb.warehouseId}) as wh_name,
        lx.sku as item_no,
        lt.lottable02 as item_barcode,
        lx.loc,
        lx.id as lpn,
        decode((select 1
        from plugin.zms_original_box_info zo
        where zo.original_box_id = lx.id
        and rownum = 1),
        '1',
        '原箱',
        '混箱') as lpn_type,
        lx.status as inventory_status,
        t.hold_reason,
        b.lookup_meaning as hold_Reason_Dsc,
        lx.qty,
        t.ADDDATE
        from ${inventoryDb.warehouseId}.lotxlocxid lx
        join ${inventoryDb.warehouseId}.lotattribute lt
        on lx.lot = lt.lot
        and lx.storerkey = lt.storerkey
        join ${inventoryDb.warehouseId}.loc lc
        on lc.loc = lx.loc
        join plugin.inventoryhold_record t
        on t.whseid = lx.whseid
        and t.item_no = lx.sku
        and t.item_barcode = lt.lottable02
        and t.hold_status = 0 --失效
        join plugin.sys_lookup_values b
        on b.lookup_meaning = t.hold_reason
        and b.lookup_type = '1000037'
        and b.enabled_flag = 'Y'
        and b.attribute1 = '质量冻结'
        where lx.storerkey = 'ZTE'
        and lx.qty > 0
        and lc.locationtype = 'PENDINGQC'
        and exists (select 1
        from ${inventoryDb.warehouseId}.sku sk
        where sk.sku = t.item_no
        and sk.storerkey = 'ZTE'
        and sk.customer_control_type = 1)
    </foreach>) s
        order by s.ADDDATE desc, s.whseid, item_barcode)
        where 1=1
        <if test="dto.startRow != null and  dto.startRow != '' and dto.endRow != null and dto.endRow != ''  ">
            and rn between #{dto.startRow ,jdbcType=INTEGER} and #{dto.endRow,jdbcType=INTEGER}
        </if>
    </select>

    <select id="selectSyncData" resultMap="BaseResultMap">
        select * from
        (<foreach collection="zteWarehouseList" item="inventoryDb" index="index" separator="union all">
            select
            'PendingIn' AS RECORD_TYPE,
            IT.SERIALKEY AS ITRN_SERIALKEY,
            IT.WHSEID
            FROM ${inventoryDb.warehouseId}.ITRN it
            JOIN ${inventoryDb.warehouseId}.LOC lc
            ON LC.LOC = IT.TOLOC
            WHERE IT.TRANTYPE = 'MV'
            AND LC.LOCATIONTYPE = 'PENDINGQC'
            AND IT.FROMID = IT.TOID
            AND EXISTS (SELECT 1
            FROM ${inventoryDb.warehouseId}.SKU sk
            WHERE SK.STORERKEY = 'ZTE'
            AND SK.CUSTOMER_CONTROL_TYPE = 1
            AND SK.SKU = IT.SKU)
            <if test="executeDate != null and executeDate != ''">
            and it.EDITDATE <![CDATA[>=]]> to_date(#{executeDate,jdbcType=VARCHAR},'yyyy-mm-dd hh24:mi:ss') - (5/24/60)
            </if>
            UNION ALL
            SELECT
            'PendingOut' AS RECORD_TYPE,
            it.serialkey as itrn_serialkey,
            IT.WHSEID
            FROM ${inventoryDb.warehouseId}.ITRN it
            JOIN ${inventoryDb.warehouseId}.LOC lc
            ON LC.LOC = IT.FROMLOC
            WHERE IT.TRANTYPE = 'MV'
            AND LC.LOCATIONTYPE = 'PENDINGQC'
            AND IT.FROMID = IT.TOID
            AND EXISTS (SELECT 1
            FROM ${inventoryDb.warehouseId}.SKU sk
            WHERE SK.STORERKEY = 'ZTE'
            AND SK.CUSTOMER_CONTROL_TYPE = 1
            AND SK.SKU = IT.SKU)
            <if test="executeDate != null and executeDate != ''">
            and it.EDITDATE <![CDATA[>=]]> to_date(#{executeDate,jdbcType=VARCHAR},'yyyy-mm-dd hh24:mi:ss') - (5/24/60)
            </if>
        </foreach>) tmp
        where NOT EXISTS (SELECT 1
        FROM plugin.PENDING_PUSH_ALI_RECORD d
        WHERE d.ITRN_SERIALKEY = tmp.itrn_serialkey
            and d.WHSEID = tmp.WHSEID
        )
    </select>

    <insert id="batchInsertPendingPushAliRecord">
        insert into plugin.PENDING_PUSH_ALI_RECORD
        (RECORD_ID, RECORD_TYPE, ITRN_SERIALKEY, WHSEID,
        MESSAGE_ID, MESSAGE_TYPE, SEND_STATUS, SEND_TIMES,
        ADDDATE, ADDWHO, EDITDATE, EDITWHO, ENABLED_FLAG)
        select plugin.SEQ_PENDING_PUSH_ALI_RECORD.nextval, tmp.*
        from (
        <foreach collection="list" item="item" index="index" separator=" UNION ALL ">
        SELECT
        #{item.recordType,jdbcType=NVARCHAR} RECORD_TYPE,
        #{item.itrnSeriallKey,jdbcType=NUMERIC} ITRN_SERIALKEY,
        #{item.whseId,jdbcType=NVARCHAR} WHSEID,
        #{item.messageId,jdbcType=NVARCHAR} MESSAGE_ID,
        #{item.messageType,jdbcType=NVARCHAR} MESSAGE_TYPE,
        '-1' SEND_STATUS,
        0 SEND_TIMES,
        SYSDATE ADDDATE,
        'SYSTEM' ADDWHO,
        SYSDATE EDITDATE,
        'SYSTEM' EDITWHO,
        'Y' ENABLED_FLAG
        FROM DUAL
         </foreach>) tmp
    </insert>

    <select id="selectWaitPushPendingRecord" resultMap="BaseResultMap">
        select <include refid="Base_Column_List" />
        from plugin.PENDING_PUSH_ALI_RECORD
        where ENABLED_FLAG = 'Y'
        AND SEND_STATUS = '-1'
        AND SEND_TIMES <![CDATA[<]]> 4
    </select>

    <update id="batchUpdateSendStatus">
        UPDATE plugin.PENDING_PUSH_ALI_RECORD SET
        SEND_STATUS = #{status,jdbcType=NVARCHAR},
        <if test="updateSendTimesFlag">
            SEND_TIMES = SEND_TIMES + 1,
        </if>
        EDITDATE = SYSDATE
        WHERE RECORD_ID IN
        <foreach item='item' collection='recordIds' open='(' separator=',' close=')'>
            #{item,jdbcType=NUMERIC}
        </foreach>
    </update>

    <select id="selectPushAliData" resultType="com.zte.interfaces.infor.dto.PendingPushAliRecordDTO">
        <foreach collection="whseIdList" item="whseId" index="index" separator="union all">
            SELECT R.RECORD_ID,
            R.RECORD_TYPE,
            IT.SERIALKEY AS itrnSeriallKey,
            IT.WHSEID,
            R.MESSAGE_ID,
            R.MESSAGE_TYPE,
            IT.FROMID AS LPN,
            DECODE((SELECT '1'
            FROM PLUGIN.ZMS_ORIGINAL_BOX_INFO ZO
            WHERE ZO.ORIGINAL_BOX_ID = IT.FROMID
            AND ROWNUM = 1),
            '1',
            'original',
            'mix') AS LPNTYPE,
            IT.QTY AS ITRNQTY,
            SN.SN
            FROM PLUGIN.PENDING_PUSH_ALI_RECORD R
            JOIN ${whseId}.ITRN IT
            ON IT.SERIALKEY = R.ITRN_SERIALKEY
            AND IT.WHSEID = R.WHSEID
            LEFT JOIN ${whseId}.SN SN
            ON SN.ID = IT.FROMID
            WHERE R.RECORD_ID IN
            <foreach collection="recordIds" item="recordId" index="index" separator="," open="(" close=")">
                #{recordId}
            </foreach>
        </foreach>
    </select>

    <update id="updateSendStatus">
        UPDATE PLUGIN.PENDING_PUSH_ALI_RECORD SET
        SEND_STATUS = #{status,jdbcType=NVARCHAR},
        EDITDATE = SYSDATE
        WHERE MESSAGE_ID = #{messageId,jdbcType=NVARCHAR}
    </update>

</mapper>

