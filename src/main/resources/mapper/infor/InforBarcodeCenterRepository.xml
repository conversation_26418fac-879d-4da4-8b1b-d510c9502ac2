<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.domain.model.infor.InforBarcodeCenterRepository">
	<resultMap id="TransferReelidRelationshipMap" type="com.zte.interfaces.infor.dto.TransferReelidRelationshipDTO">
		<result column="externreceiptkey" jdbcType="VARCHAR" property="externreceiptkey" />
        <result column="whseid" jdbcType="VARCHAR" property="whseid" />
        <result column="oldSku" jdbcType="VARCHAR" property="oldSku" />
        <result column="oldLottable02" jdbcType="VARCHAR" property="oldLottable02" />
        <result column="oldSerialnumber" jdbcType="VARCHAR" property="oldSerialnumber" />
        <result column="newSku" jdbcType="VARCHAR" property="newSku" />
        <result column="newLottable02" jdbcType="VARCHAR" property="newLottable02" />
        <result column="newSerialnumber" jdbcType="VARCHAR" property="newSerialnumber" />
        <result column="adddate" jdbcType="DATE" property="adddate" />
        <result column="addwho" jdbcType="VARCHAR" property="addwho" />
        <result column="editdate" jdbcType="DATE" property="editdate" />
        <result column="editwho" jdbcType="VARCHAR" property="editwho" />
        <result column="issend" jdbcType="DECIMAL" property="issend" />
        <result column="sendtimes" jdbcType="DECIMAL" property="sendtimes" />
        <result column="requestparam" jdbcType="VARCHAR" property="requestparam" />
	</resultMap>
	<select id="getInventoryQty"  resultType="java.lang.Double" >
		select nvl(sum(l.qty),0)
		from ${wmwhseId}.lotattribute lt
		join ${wmwhseId}.lot l
		on lt.lot = l.lot
		and lt.storerkey = 'ZTE'
		and l.storerkey = 'ZTE'
		where lt.lottable02 = #{itemBarcode}
	</select>
	<select id="getTransferReelidRelationship" parameterType="com.zte.interfaces.infor.dto.TransferReelidRelationshipDTO" resultMap="TransferReelidRelationshipMap">
			select externreceiptkey,
			whseid,
			old_serialnumber oldSerialnumber,
			old_lottable02 oldLottable02,
			old_sku oldSku,
			new_serialnumber  newSerialnumber,
			new_lottable02 newLottable02,
			new_sku newSku,
			issend,
			sendtimes,
			requestparam
			from plugin.transfer_reelid_relationship
			where issend in (-1, 2) and <![CDATA[sendtimes < 4]]>
		<if test = "externreceiptkey != null and externreceiptkey !='' " >
			and externreceiptkey = #{externreceiptkey, jdbcType=VARCHAR}
		</if>
		<if test = "newSerialnumber != null and newSerialnumber !='' " >
			and newSerialnumber = #{newSerialnumber, jdbcType=VARCHAR}
		</if>
	</select>

	<update id="updateTransferReelidRelationship" parameterType="com.zte.interfaces.infor.dto.TransferReelidRelationshipDTO">
		<foreach collection="list" item="item" index="index"   separator=";" open="begin" close=";end;">
			update plugin.transfer_reelid_relationship
			<set>
				issend        = #{item.issend,jdbcType=DECIMAL},
				sendtimes  	  = sendtimes + 1,
				requestparam  = #{item.requestparam,jdbcType=VARCHAR},
				editdate   	  = sysdate,
				editwho       = #{item.editwho,jdbcType=VARCHAR}
			</set>
			 where externreceiptkey = #{item.externreceiptkey, jdbcType=VARCHAR}
			   and new_serialnumber = #{item.newSerialnumber, jdbcType=VARCHAR}
		</foreach>
	</update>

	<resultMap id="SkuDefaultResultMap" type="com.zte.interfaces.infor.dto.SkuDTO">
		<result column="SKU" jdbcType="OTHER" property="sku" />
		<result column="LOC" jdbcType="OTHER" property="loc" />
		<result column="PUTAWAYZONE" jdbcType="OTHER" property="putAwayZone" />
		<result column="DESCR" jdbcType="OTHER" property="descr" />
		<result column="BUSR4" jdbcType="OTHER" property="bsur" />
	</resultMap>
	<select id="getSkuNameList" parameterType="java.lang.String" resultMap="SkuDefaultResultMap">
		select t.descr DESCR,t.sku SKU
		from enterprise.sku t
		where t.storerkey = 'ZTE'
		and t.sku in
		<foreach collection="list" open="(" separator="," close=")" item="item">
			#{item,jdbcType=VARCHAR}
		</foreach>
	</select>
    <select id="getInventoryQtyMap" resultType="com.zte.interfaces.infor.vo.ItemBarcodeWmIdVO">
		select lt.lottable02 itemBarcode ,nvl(sum(l.qty),0) qty
		from ${wmwhseId}.lotattribute lt
		join ${wmwhseId}.lot l
		on lt.lot = l.lot
		and lt.storerkey = 'ZTE'
		and l.storerkey = 'ZTE'
		where lt.lottable02  in
		<foreach collection="itemBarcodes" index="index" item="itemBarcode" open="(" separator="," close=")">
			#{itemBarcode}
		</foreach>
		group by lt.lottable02
	</select>

	<select id="getEnvWmwhids" resultType="java.lang.String">
		SELECT UPPER(DB_LOGID)  FROM wmsadmin.pl_db where isactive =1 and db_enterprise=0 and db_type not in (7,8)
	</select>

	<select id="getInventoryHoldWmwhids" resultType="java.lang.String">
		SELECT UPPER(DB_LOGID)  FROM wmsadmin.pl_db where isactive =1 and db_enterprise=0 and db_type in (1,2,6)
	</select>

	<!-- 不良品库存对应的仓库 -->
	<select id="getBadInventoryHoldWmwhids" resultType="java.lang.String">
		SELECT UPPER(DB_LOGID)  FROM wmsadmin.pl_db where isactive =1 and db_enterprise=0 and db_type in (4)
	</select>

</mapper>