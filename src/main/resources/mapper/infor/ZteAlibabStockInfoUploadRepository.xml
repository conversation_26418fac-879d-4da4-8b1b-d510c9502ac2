<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.domain.model.infor.ZteAlibabStockInfoUploadRepository">

    <select id="getInforWarehouseList" resultType="com.zte.interfaces.step.dto.ZteWarehouseInfoDTO">
        SELECT UPPER(PB.DB_LOGID) as warehouseId,
        PB.DB_TYPE as dbType
        FROM WMSADMIN.PL_DB PB
        WHERE PB.ISACTIVE = 1
        AND PB.DB_ENTERPRISE = 0
        AND PB.DB_TYPE in (1,2,4)
    </select>

    <select id="getALiStockInfoStaticsList" parameterType="com.zte.interfaces.step.dto.ZteWarehouseInfoDTO"
            resultType="com.zte.interfaces.step.dto.CustomerInventoryPickUpDTO">
        <if test="dbType != 4">
            SELECT sk.whseid,
            sk.sku,
            sk.customer_item_no as mpn,
            lx.qty as vendorInventoryQuantity,
            TO_CHAR(lt.lottable11, 'YYYYMMDD') as inventoryBatch,
            sk.customer_control_type as customerControlType,
            '0' as inventoryType,
            0 as itemType
            FROM ${warehouseId}.SKU sk
            JOIN ${warehouseId}.lotxlocxid lx
            on sk.sku = lx.sku
            and sk.storerkey = lx.storerkey
            JOIN ${warehouseId}.lotattribute lt
            on lt.lot = lx.lot
            and lt.sku = lx.sku
            and lt.storerkey = lx.storerkey
            JOIN ${warehouseId}.loc loc
            on lx.loc = loc.loc
            WHERE sk.customer_control_type = 1
            and sk.storerkey = 'ZTE'
            and lx.id != ' '
            and lx.qty > 0
            and loc.locationtype <![CDATA[<>]]> 'PENDINGQC'
            and not exists (
            SELECT 1
            FROM ${warehouseId}.inventoryhold ii
            WHERE ii.id = lx.id
            and ii.status = 'QCFAILED'
            )
            UNION ALL
            SELECT sk.whseid,
            sk.sku,
            sk.customer_item_no as mpn,
            lx.qty as vendorInventoryQuantity,
            TO_CHAR(lt.lottable11, 'YYYYMMDD') as inventoryBatch,
            sk.customer_control_type as customerControlType,
            '5' as inventoryType,
            0 as itemType
            FROM ${warehouseId}.SKU sk
            JOIN ${warehouseId}.lotxlocxid lx
            on sk.sku = lx.sku
            and sk.storerkey = lx.storerkey
            JOIN ${warehouseId}.lotattribute lt
            on lt.lot = lx.lot
            and lt.sku = lx.sku
            and lt.storerkey = lx.storerkey
            JOIN ${warehouseId}.inventoryhold ii on ii.id = lx.id
            WHERE sk.customer_control_type = 1
            and sk.storerkey = 'ZTE'
            and lx.id != ' '
            and lx.qty > 0
            and ii.status = 'QCFAILED'
            UNION ALL
            SELECT sk.whseid,
            sk.sku,
            sk.customer_item_no as mpn,
            lx.qty as vendorInventoryQuantity,
            TO_CHAR(lt.lottable11, 'YYYYMMDD') as inventoryBatch,
            sk.customer_control_type as customerControlType,
            '10' as inventoryType,
            0 as itemType
            FROM ${warehouseId}.SKU sk
            JOIN ${warehouseId}.lotxlocxid lx
            on sk.sku = lx.sku
            and sk.storerkey = lx.storerkey
            JOIN ${warehouseId}.lotattribute lt
            on lt.lot = lx.lot
            and lt.sku = lx.sku
            and lt.storerkey = lx.storerkey
            JOIN ${warehouseId}.loc loc
            on lx.loc = loc.loc
            WHERE sk.customer_control_type = 1
            and sk.storerkey = 'ZTE'
            and lx.id != ' '
            and lx.qty > 0
            and loc.locationtype = 'PENDINGQC'
            and not exists (
                SELECT 1
                FROM ${warehouseId}.inventoryhold ii
                WHERE ii.id = lx.id
                and ii.status = 'QCFAILED'
            )
        </if>
        <if test="dbType == 4">
            SELECT sk.whseid,
            sk.sku,
            sk.customer_item_no as mpn,
            lx.qty as vendorInventoryQuantity,
            TO_CHAR(lt.lottable11, 'YYYYMMDD') as inventoryBatch,
            sk.customer_control_type as customerControlType,
            '5' as inventoryType,
            0 as itemType
            FROM ${warehouseId}.SKU sk
            JOIN ${warehouseId}.lotxlocxid lx
            on sk.sku = lx.sku
            and sk.storerkey = lx.storerkey
            JOIN ${warehouseId}.lotattribute lt
            on lt.lot = lx.lot
            and lt.sku = lx.sku
            and lt.storerkey = lx.storerkey
            WHERE sk.customer_control_type = 1
            and sk.storerkey = 'ZTE'
            and lx.id != ' '
            and lx.qty > 0
        </if>
    </select>

    <update id="updateInventoryStaticsData">
        update plugin.zms_inventory_statistics zis set zis.enabled_flag = 'N'
        where zis.enabled_flag = 'Y'
        AND zis.CREATION_DATE <![CDATA[>=]]> TRUNC(SYSDATE)
        AND zis.CREATION_DATE <![CDATA[<]]> TRUNC(SYSDATE) + 1
    </update>

    <insert id="addInventoryStaticsData" parameterType="com.zte.interfaces.step.dto.CustomerInventoryPickUpDTO">
        <foreach collection="list" item="item" separator=";" open="begin" close=";end;">
            INSERT INTO plugin.zms_inventory_statistics
            (SERIALKEY, SOURCE_SYSTEM, WHSEID, SKU, MPN,
            INVENTORY_BATCH, VENDOR_INVENTORY_QUANTITY, INVENTORY_TYPE, CUSTOMER_CONTROL_TYPE, ITEM_TYPE, CONFIG_MODEL,
            DELIVERED_QUANTITY, CREATED_BY, LAST_UPDATED_BY)
            VALUES
            (plugin.ZMS_INVENTORY_STATISTICS_S.NEXTVAL,
            #{item.sourceSystem},
            #{item.whseid},
            #{item.sku},
            #{item.mpn},
            #{item.inventoryBatch},
            #{item.vendorInventoryQuantity},
            #{item.inventoryType},
            #{item.customerControlType},
            #{item.itemType},
            #{item.configModel},
            #{item.deliveredQuantity},
            #{item.createdBy},
            #{item.lastUpdatedBy}
            )
        </foreach>
    </insert>

    <select id="getInventoryStatisticsData" resultType="com.zte.interfaces.step.dto.CustomerInventoryLinesDTO">
        select
        zis.mpn,
        zis.inventory_batch as inventoryBatch,
        sum(zis.vendor_inventory_quantity) as vendorInventoryQuantity,
        zis.inventory_type as inventoryType,
        zis.item_type as itemType,
        zis.config_model as configModel,
        zis.delivered_quantity as deliveredQuantity
        from plugin.zms_inventory_statistics zis
        where
        zis.enabled_flag = 'Y'
        and zis.MERGED_FLAG = 'N'
        and TRUNC(zis.last_update_date) = TRUNC(SYSDATE)
        group by
        zis.mpn,
        zis.inventory_batch,
        zis.inventory_type,
        zis.item_type,
        zis.config_model,
        zis.delivered_quantity
    </select>

    <update id="disableForInventoryStaticsData">
        update plugin.zms_inventory_statistics zis
        set zis.merged_flag = 'Y'
        where zis.enabled_flag = 'Y'
        and zis.merged_flag = 'N'
    </update>

    <update id="updateInventoryMergedData">
        update plugin.zms_inventory_merged zim
        set zim.enabled_flag = 'N'
        where zim.enabled_flag = 'Y'
        AND zim.CREATION_DATE <![CDATA[>=]]> TRUNC(SYSDATE)
        AND zim.CREATION_DATE <![CDATA[<]]> TRUNC(SYSDATE) + 1
    </update>

    <insert id="addInventoryMergedData" parameterType="com.zte.interfaces.step.dto.CustomerInventoryLinesDTO">
        <foreach collection="list" item="item" separator=";" open="begin" close=";end;">
            INSERT INTO plugin.zms_inventory_merged
            (SERIALKEY, MPN, INVENTORY_BATCH, VENDOR_INVENTORY_QUANTITY, INVENTORY_TYPE,
            INVENTORY_DIRECTIVE, VERSION_SEQ_NO, MESSAGE_ID, MESSAGE_TYPE, FACTORY_CODE,
            CUSTOMER_INVENTORY_LINE_NUMBER,FINISH_FLAG,LOCATOR_TYPE,CARTON_ID,VENDOR_CARTON_QUANTITY,
            SN,ITEM_TYPE, CREATED_BY, LAST_UPDATED_BY, CONFIG_MODEL, DELIVERED_QUANTITY)
            VALUES
            (plugin.ZMS_INVENTORY_MERGED_S.NEXTVAL,
            #{item.mpn,jdbcType=VARCHAR},
            #{item.inventoryBatch,jdbcType=VARCHAR},
            #{item.vendorInventoryQuantity,jdbcType=INTEGER},
            #{item.inventoryType,jdbcType=VARCHAR},
            #{item.inventoryDirective,jdbcType=VARCHAR},
            #{item.versionSeqNo,jdbcType=VARCHAR},
            #{item.messageId,jdbcType=VARCHAR},
            #{item.messageType,jdbcType=VARCHAR},
            #{item.factoryCode,jdbcType=VARCHAR},
            #{item.customerInventoryLineNumber,jdbcType=VARCHAR},
            #{item.finishFlag,jdbcType=INTEGER},
            #{item.locatorType,jdbcType=INTEGER},
            #{item.cartonId,jdbcType=VARCHAR},
            #{item.vendorCartonQuantity,jdbcType=INTEGER},
            #{item.sn,jdbcType=VARCHAR},
            #{item.itemType,jdbcType=INTEGER},
            #{item.createdBy,jdbcType=VARCHAR},
            #{item.lastUpdatedBy,jdbcType=VARCHAR},
            #{item.configModel,jdbcType=VARCHAR},
            #{item.deliveredQuantity,jdbcType=INTEGER}
            )
        </foreach>
    </insert>

    <select id="getInventoryMergedSerialKey" resultType="java.lang.Integer">
        SELECT plugin.ZMS_INVENTORY_MERGED_S.NEXTVAL FROM DUAL
    </select>

    <update id="updateInventoryMergedStatusProcessing"
            parameterType="com.zte.interfaces.step.dto.CustomerInventoryLinesDTO">
        update plugin.zms_inventory_merged zim
        set
        zim.last_updated_by = #{lastUpdatedBy},
        zim.last_update_date = sysdate,
        zim.send_status = '0'
        where zim.enabled_flag = 'Y'
        and zim.send_status = '-1'
        and zim.message_id = #{messageId}
    </update>

    <update id="updateInventoryMergedStatus" parameterType="com.zte.interfaces.step.dto.CustomerInventoryLinesDTO">
        update plugin.zms_inventory_merged zim
        set
        zim.last_update_date = sysdate,
        zim.send_status = #{sendStatus},
        zim.send_times = CASE WHEN #{sendStatus} = '-1' THEN zim.send_times +1 ELSE zim.send_times END
        where zim.enabled_flag = 'Y'
        and zim.send_status = '0'
        and zim.message_id = #{messageId}
    </update>

    <update id="updateInventoryMergedStatusExt" parameterType="com.zte.interfaces.step.dto.CustomerInventoryLinesDTO">
        update plugin.zms_inventory_merged zim
        set
        zim.last_updated_by = #{lastUpdatedBy},
        zim.last_update_date = sysdate,
        zim.send_status = #{sendStatus},
        zim.send_times = CASE WHEN #{sendStatus} = '0' THEN zim.send_times +1 ELSE zim.send_times END
        where zim.enabled_flag = 'Y'
        and zim.message_id = #{messageId}
    </update>
    <select id="getInventoryMergedData" parameterType="com.zte.interfaces.step.dto.CustomerInventoryLinesDTO"
            resultType="com.zte.interfaces.step.dto.CustomerInventoryLinesDTO">
        select
        zim.mpn,
        zim.inventory_batch as inventoryBatch,
        zim.vendor_inventory_quantity as vendorInventoryQuantity,
        zim.inventory_type as inventoryType,
        zim.customer_inventory_line_number as customerInventoryLineNumber,
        zim.inventory_directive as inventoryDirective,
        zim.version_seq_no as versionSeqNo,
        zim.message_id as messageId,
        zim.message_id as keywords,
        zim.item_type as itemType,
        zim.config_model as configModel,
        zim.delivered_quantity as deliveredQuantity
        from plugin.zms_inventory_merged zim
        where
        zim.enabled_flag = 'Y'
        and zim.send_status = '-1'
        and zim.SEND_TIMES &lt; 5
        and zim.message_type = #{messageType}
        <choose>
            <when test="uploadDateBegin != null and uploadDateBegin != ''
                and uploadDateEnd != null and uploadDateEnd != ''">
                AND zim.last_update_date <![CDATA[>=]]> to_date(#{uploadDateBegin},'yyyy-mm-dd hh24:mi:ss')
                AND zim.last_update_date <![CDATA[<=]]> to_date(#{uploadDateEnd},'yyyy-mm-dd hh24:mi:ss')
            </when>
            <otherwise>
                AND TRUNC(zim.last_update_date) = TRUNC(SYSDATE)
            </otherwise>
        </choose>
    </select>
    <select id="getInventoryDataByCurrent" resultType="com.zte.interfaces.step.dto.CustomerInventoryPickUpDTO">
        SELECT ZIS.SOURCE_SYSTEM as sourceSystem,
        ZIS.MPN,
        ZIS.VENDOR_INVENTORY_QUANTITY AS vendorInventoryQuantity,
        ZIS.INVENTORY_TYPE AS inventoryType
        FROM PLUGIN.ZMS_INVENTORY_STATISTICS ZIS
        WHERE ZIS.ENABLED_FLAG = 'Y'
        AND ZIS.CUSTOMER_CONTROL_TYPE = 1
        AND ZIS.INVENTORY_TYPE in ('0','1')
        AND ZIS.CREATION_DATE <![CDATA[>=]]> TRUNC(SYSDATE)
        AND ZIS.CREATION_DATE <![CDATA[<]]> TRUNC(SYSDATE) + 1
    </select>
    <select id="getAllUnsuccessfulInventoryMergedData" parameterType="com.zte.interfaces.step.dto.CustomerInventoryLinesDTO"
            resultType="com.zte.interfaces.step.dto.CustomerInventoryLinesDTO">
        select
        zim.inventory_type as inventoryType,
        zim.inventory_directive as inventoryDirective,
        zim.factory_code as factoryCode,
        zim.version_seq_no as versionSeqNo,
        zim.finish_flag as finishFlag,
        zim.customer_inventory_line_number as customerInventoryLineNumber,
        zim.mpn,
        zim.locator_type as locatorType,
        zim.inventory_batch as inventoryBatch,
        zim.carton_id as cartonId,
        zim.vendor_carton_quantity as vendorCartonQuantity,
        zim.vendor_inventory_quantity as vendorInventoryQuantity,
        zim.sn,
        zim.item_type as itemType,
        zim.message_id as messageId,
        zim.message_type as messageType,
        zim.send_status sendStatus,
        zim.send_times sendTimes
        from plugin.zms_inventory_merged zim
        where
        zim.enabled_flag = 'Y'
        <if test="messageId != null and messageId != ''">
            and zim.message_id = #{messageId}
        </if>
        <if test="messageType != null and messageType != ''">
            and zim.message_type = #{messageType}
        </if>
        <if test="sendStatus != null and sendStatus != ''">
            and zim.send_status = #{sendStatus}
        </if>
        <if test="finishFlag != null">
            and zim.finish_flag = #{finishFlag}
        </if>
        <if test="messageId == null or messageId == ''">
            and zim.SEND_TIMES <![CDATA[<]]> 4
            and zim.creation_date <![CDATA[>=]]> TRUNC(SYSDATE)
        </if>
    </select>

    <select id="getPartsReceiptList" parameterType="com.zte.action.iscpedi.model.IscpEdiLog" resultType="com.zte.interfaces.step.dto.AliDeliveryReceiptQueryDTO">
        --阿里管控，一码通管控，原箱
        SELECT distinct rd.serialkey,
        ep.hsusr1 AS actualDepartureNoteNumber,
        ep.hsusr2 AS deliveryNote,
        ep.hsusr3 AS brand,
        s.customer_item_no AS mpn,
        rd.lottable11 AS actualTimert,
        ep.susr1 AS asnLine,
        ep.susr2 AS coo,
        ep.susr3 AS customerPoLineNumber,
        TO_CHAR(rd.lottable05, 'YYYYMMDD') AS aliInvBatch,
        '1' AS tag,
        rd.qtyreceived AS specifications,
        rd.toid AS skuId,
        ep.externreceiptkey,
        ep.ref02 as customerPoNumber
        FROM ${whseid}.receiptdetail rd
        JOIN plugin.edi_po ep
        ON rd.externreceiptkey = ep.externreceiptkey
        AND rd.whseid = ep.whseid
        AND rd.receiptkey = ep.receiptkey
        AND rd.lottable02 = ep.lottable02
        JOIN ${whseid}.sku s
        ON rd.sku = s.sku
        AND s.storerkey = 'ZTE'
        AND s.customer_control_type = 1
        AND s.serialprocess = 3
        AND s.busr8 = '10'
        WHERE
        ep.symbol = '1'
        AND rd.qtyreceived > 0
        AND rd.externreceiptkey = #{externkey, jdbcType = VARCHAR}
        AND rd.receiptkey = #{sourcekey, jdbcType = VARCHAR}
        AND EXISTS (
        SELECT 1
        FROM plugin.zms_original_box_info zo
        WHERE zo.original_box_id = rd.toid
        )
        UNION ALL
        --阿里管控，一码通管控，混箱
        SELECT distinct rd.serialkey,
        ep.hsusr1 AS actualDepartureNoteNumber,
        ep.hsusr2 AS deliveryNote,
        ep.hsusr3 AS brand,
        s.customer_item_no AS mpn,
        rd.lottable11 AS actualTimert,
        ep.susr1 AS asnLine,
        ep.susr2 AS coo,
        ep.susr3 AS customerPoLineNumber,
        TO_CHAR(rd.lottable05, 'YYYYMMDD') AS aliInvBatch,
        '2' AS tag,
        1 AS specifications,
        sn.sn AS skuId,
        ep.externreceiptkey,
        ep.ref02 as customerPoNumber
        FROM ${whseid}.receiptdetail rd
        JOIN plugin.edi_po ep
        ON rd.externreceiptkey = ep.externreceiptkey
        AND rd.whseid = ep.whseid
        AND rd.receiptkey = ep.receiptkey
        AND rd.lottable02 = ep.lottable02
        JOIN ${whseid}.sku s
        ON rd.sku = s.sku
        AND s.storerkey = 'ZTE'
        AND s.customer_control_type = 1
        AND s.serialprocess = 3
        AND s.busr8 = '10'
        JOIN plugin.zms_sn_bound_detail sn
        ON sn.externalkey = ep.externreceiptkey
        AND sn.whseid = ep.whseid
        AND sn.pkg_id = rd.toid
        WHERE
        ep.symbol = '1'
        AND rd.qtyreceived > 0
        AND rd.externreceiptkey = #{externkey, jdbcType = VARCHAR}
        AND rd.receiptkey = #{sourcekey, jdbcType = VARCHAR}
        AND NOT EXISTS (
        SELECT 1
        FROM plugin.zms_original_box_info zo
        WHERE zo.original_box_id = rd.toid
        )
        UNION ALL
        --阿里管控，非一码通管控
        SELECT distinct rd.serialkey,
        ep.hsusr1 AS actualDepartureNoteNumber,
        ep.hsusr2 AS deliveryNote,
        ep.hsusr3 AS brand,
        s.customer_item_no AS mpn,
        rd.lottable11 AS actualTimert,
        ep.susr1 AS asnLine,
        ep.susr2 AS coo,
        ep.susr3 AS customerPoLineNumber,
        TO_CHAR(rd.lottable05, 'YYYYMMDD') AS aliInvBatch,
        '3' AS tag,
        rd.qtyreceived AS specifications,
        null AS skuId,
        ep.externreceiptkey,
        ep.ref02 as customerPoNumber
        FROM ${whseid}.receiptdetail rd
        JOIN plugin.edi_po ep
        ON rd.externreceiptkey = ep.externreceiptkey
        AND rd.whseid = ep.whseid
        and rd.receiptkey = ep.receiptkey
        and rd.lottable02 = ep.lottable02
        JOIN ${whseid}.sku s
        ON rd.sku = s.sku
        AND s.storerkey = 'ZTE'
        AND s.customer_control_type = 1
        AND s.serialprocess != 3
        AND s.busr8 = '10'
        WHERE
        ep.symbol = '1'
        AND rd.qtyreceived > 0
        AND rd.externreceiptkey = #{externkey, jdbcType = VARCHAR}
        AND rd.receiptkey = #{sourcekey, jdbcType = VARCHAR}
    </select>
</mapper>