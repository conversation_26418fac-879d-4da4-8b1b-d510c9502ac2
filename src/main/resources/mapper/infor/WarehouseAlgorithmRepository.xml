<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.domain.model.infor.WarehouseAlgorithmRepository">

	<select id="getWarehouseAlgorithmList" parameterType="com.zte.interfaces.infor.dto.WarehouseAlgorithmDTO" resultType="com.zte.interfaces.infor.dto.WarehouseAlgorithmDTO">
		select s.* from (
		select t.serialkey,
		t.whseid,
		f.DESCRIPTION as whName,
		t.warehouse,
		slv1.description warehouseDesc,
		t.warehouse_area as warehouseArea,
		slv2.description warehouseAreaDesc,
		t.scheme_type as schemeType,
		t.algorithm_scheme as algorithmScheme,
		t.whole_warehouse_strategy as wholeWarehouseStrategy,
		t.max_warehouse_adjust as maxWarehouseAdjust,
		t.unit_whole_warehouse_work as unitWholeWarehouseWork,
		t.start_coord_x as startCoordX,
		t.start_coord_y as startCoordY,
		t.end_coord_x as endCoordX,
		t.end_coord_y as endCoordY,
		t.execution_frequency as executionFrequency,
		t.enabled_flag as enabledFlag,
		decode(t.enabled_flag,'Y','有效','N','无效','') as enabledFlagDesc,
		t.created_by as createdBy,
		t.creation_date as creationDate,
		t.last_updated_by as lastUpdatedBy,
		t.last_update_date as lastUpdateDate,
		t.whole_warehouse_manpower as wholeWarehouseManpower,
		t.min_whole_warehouse_unit as minWholeWarehouseUnit,
		rownum rn
		from plugin.warehouse_algorithm t
		left join ENTERPRISE.FACILITYNEST f
		on t.whseid = upper(substr(f.name, 7))
		left join plugin.sys_lookup_values slv1
		on slv1.lookup_type = '1000064' and slv1.lookup_meaning = t.warehouse
		left join plugin.sys_lookup_values slv2
		on slv2.lookup_type = '1000065' and slv2.lookup_meaning = t.warehouse_area
		where slv1.enabled_flag = 'Y'
		and slv2.enabled_flag = 'Y'
		<if test="whseid != null and whseid !='' ">
			and t.whseid  = #{whseid,jdbcType=VARCHAR}
		</if>
		<if test="warehouse != null and warehouse !='' ">
			and t.warehouse  = #{warehouse,jdbcType=VARCHAR}
		</if>
		<if test="warehouseArea != null and warehouseArea !='' ">
			and t.warehouse_area  = #{warehouseArea,jdbcType=VARCHAR}
		</if>
		<if test="schemeType != null and schemeType !='' ">
			and t.scheme_type = #{schemeType,jdbcType=VARCHAR}
		</if>
		<if test="algorithmScheme != null and algorithmScheme !='' ">
			and t.algorithm_scheme = #{algorithmScheme,jdbcType=VARCHAR}
		</if>
		<if test="enabledFlag != null and enabledFlag !='' ">
			and t.enabled_flag  = #{enabledFlag,jdbcType=VARCHAR}
		</if>
		<if test="createdBy != null and createdBy !='' ">
			and t.created_by  = #{createdBy,jdbcType=VARCHAR}
		</if>
		<if test="algorithmSchemeList != null and algorithmSchemeList.size() > 0">
			and t.algorithm_scheme in
			<foreach collection="algorithmSchemeList" index="index" item="item" open="(" separator="," close=")">
				#{item,jdbcType=VARCHAR}
			</foreach>
		</if>
		<if test="creationDateBegin != null and creationDateBegin !='' ">
			and t.creation_date <![CDATA[>=]]> to_date(#{creationDateBegin,jdbcType=VARCHAR},'yyyy-mm-dd hh24:mi:ss')
		</if>
		<if test="creationDateEnd != null and creationDateEnd !='' ">
			and t.creation_date <![CDATA[<=]]> to_date(#{creationDateEnd,jdbcType=VARCHAR},'yyyy-mm-dd hh24:mi:ss')
		</if>
		) s where 1=1
		<if test="startRow != null and  startRow != '' and endRow != null and endRow != ''  ">
			and  s.rn between #{startRow ,jdbcType=INTEGER} and #{endRow,jdbcType=INTEGER}
		</if>
		<if test="startRow == null or startRow == '' or endRow == null or endRow == ''  ">
			and  s.rn &lt;= 500000
		</if>
	</select>

	<select id="getWarehouseAlgorithmListVOTotal" parameterType="com.zte.interfaces.infor.dto.WarehouseAlgorithmDTO" resultType="java.lang.Integer">
		select count(1)
		from plugin.warehouse_algorithm t
		where 1 = 1
		<if test="whseid != null and whseid !='' ">
			and t.whseid  = #{whseid,jdbcType=VARCHAR}
		</if>
		<if test="warehouse != null and warehouse !='' ">
			and t.warehouse  = #{warehouse,jdbcType=VARCHAR}
		</if>
		<if test="warehouseArea != null and warehouseArea !='' ">
			and t.warehouse_area  = #{warehouseArea,jdbcType=VARCHAR}
		</if>
		<if test="schemeType != null and schemeType !='' ">
			and t.scheme_type = #{schemeType,jdbcType=VARCHAR}
		</if>
		<if test="algorithmScheme != null and algorithmScheme !='' ">
			and t.algorithm_scheme = #{algorithmScheme,jdbcType=VARCHAR}
		</if>
		<if test="enabledFlag != null and enabledFlag !='' ">
			and t.enabled_flag  = #{enabledFlag,jdbcType=VARCHAR}
		</if>
		<if test="createdBy != null and createdBy !='' ">
			and t.created_by  = #{createdBy,jdbcType=VARCHAR}
		</if>
		<if test="creationDateBegin != null and creationDateBegin !='' ">
			and t.creation_date <![CDATA[>=]]> to_date(#{creationDateBegin,jdbcType=VARCHAR},'yyyy-mm-dd hh24:mi:ss')
		</if>
		<if test="creationDateEnd != null and creationDateEnd !='' ">
			and t.creation_date <![CDATA[<=]]> to_date(#{creationDateEnd,jdbcType=VARCHAR},'yyyy-mm-dd hh24:mi:ss')
		</if>
	</select>

	<insert id="saveWarehouseAlgorithm" parameterType="com.zte.interfaces.infor.dto.WarehouseAlgorithmDTO">
		merge into plugin.warehouse_algorithm a
		using (select nvl(#{serialkey,jdbcType=VARCHAR},-1) serialkey,
		upper(#{whseid,jdbcType=VARCHAR}) whseid, #{warehouse,jdbcType=VARCHAR} warehouse, #{warehouseArea,jdbcType=VARCHAR} warehouse_area,
		#{schemeType,jdbcType=VARCHAR} scheme_type, #{algorithmScheme,jdbcType=VARCHAR} algorithm_scheme,
		#{wholeWarehouseStrategy,jdbcType=VARCHAR} whole_warehouse_strategy, #{maxWarehouseAdjust,jdbcType=DECIMAL} max_warehouse_adjust,
		#{unitWholeWarehouseWork,jdbcType=DECIMAL} unit_whole_warehouse_work, #{startCoordX,jdbcType=DECIMAL} start_coord_x,
		#{startCoordY,jdbcType=DECIMAL} start_coord_y, #{endCoordX,jdbcType=DECIMAL} end_coord_x,
		#{endCoordY,jdbcType=DECIMAL} end_coord_y, #{executionFrequency,jdbcType=DECIMAL} execution_frequency,
		#{createdBy,jdbcType=VARCHAR} created_by, #{lastUpdatedBy,jdbcType=VARCHAR} last_updated_by,
		#{wholeWarehouseManpower,jdbcType=DECIMAL} whole_warehouse_manpower,
		#{minWholeWarehouseUnit,jdbcType=DECIMAL} min_whole_warehouse_unit from dual) b
		on (a.serialkey = b.serialkey)
		when matched then
		update set a.whseid=b.whseid, a.warehouse=b.warehouse, a.warehouse_area=b.warehouse_area,
		a.scheme_type=b.scheme_type, a.algorithm_scheme=b.algorithm_scheme,
		a.whole_warehouse_strategy=b.whole_warehouse_strategy, a.max_warehouse_adjust=b.max_warehouse_adjust,
		a.unit_whole_warehouse_work=b.unit_whole_warehouse_work, a.start_coord_x=b.start_coord_x,
		a.start_coord_y=b.start_coord_y, a.end_coord_x=b.end_coord_x, a.end_coord_y=b.end_coord_y,
		a.execution_frequency=b.execution_frequency, a.last_updated_by=b.last_updated_by, a.last_update_date = sysdate,
		a.whole_warehouse_manpower=b.whole_warehouse_manpower,a.min_whole_warehouse_unit=b.min_whole_warehouse_unit
		when not matched then
		insert (a.serialkey, a.whseid, a.warehouse, a.warehouse_area, a.scheme_type,
		a.algorithm_scheme, a.whole_warehouse_strategy, a.max_warehouse_adjust, a.unit_whole_warehouse_work,
		a.start_coord_x, a.start_coord_y, a.end_coord_x, a.end_coord_y, a.execution_frequency,
		a.enabled_flag, a.created_by, a.creation_date, a.last_updated_by, a.last_update_date,
		a.whole_warehouse_manpower, a.min_whole_warehouse_unit)
		values (plugin.warehouse_algorithm_s.nextval, b.whseid, b.warehouse, b.warehouse_area,
		b.scheme_type, b.algorithm_scheme, b.whole_warehouse_strategy, b.max_warehouse_adjust,
		b.unit_whole_warehouse_work, b.start_coord_x, b.start_coord_y, b.end_coord_x, b.end_coord_y, b.execution_frequency,
		'Y', b.created_by, sysdate, b.last_updated_by, sysdate, b.whole_warehouse_manpower, b.min_whole_warehouse_unit)
	</insert>

	<insert id="saveWarehouseAlgorithmList" parameterType="java.util.List">
		insert into plugin.warehouse_algorithm (serialkey, whseid, warehouse, warehouse_area, scheme_type,
		algorithm_scheme, whole_warehouse_strategy, max_warehouse_adjust, unit_whole_warehouse_work,
		start_coord_x, start_coord_y, end_coord_x, end_coord_y, execution_frequency,
		enabled_flag, created_by, creation_date, last_updated_by, last_update_date,
		whole_warehouse_manpower, min_whole_warehouse_unit)
		select plugin.warehouse_algorithm_s.nextval, temp.* from (
		<foreach collection="list" item="item" index="index" separator="union all">
			select
			upper(#{item.whseid,jdbcType=VARCHAR}), #{item.warehouse,jdbcType=VARCHAR}, #{item.warehouseArea,jdbcType=VARCHAR}, #{item.schemeType,jdbcType=VARCHAR},
			#{item.algorithmScheme,jdbcType=VARCHAR}, #{item.wholeWarehouseStrategy,jdbcType=VARCHAR}, #{item.maxWarehouseAdjust,jdbcType=DECIMAL},
			#{item.unitWholeWarehouseWork,jdbcType=DECIMAL}, #{item.startCoordX,jdbcType=DECIMAL}, #{item.startCoordY,jdbcType=DECIMAL},
			#{item.endCoordX,jdbcType=DECIMAL}, #{item.endCoordY,jdbcType=DECIMAL}, #{item.executionFrequency,jdbcType=DECIMAL}, 'Y',
			#{item.createdBy,jdbcType=VARCHAR}, sysdate as creationDate, #{item.lastUpdatedBy,jdbcType=VARCHAR}, sysdate as lastUpdateDate,
			#{item.wholeWarehouseManpower,jdbcType=DECIMAL}, #{item.minWholeWarehouseUnit,jdbcType=DECIMAL}
			from dual
		</foreach>
		) temp
	</insert>

	<update id="updateWarehouseAlgorithm" parameterType="com.zte.interfaces.infor.dto.WarehouseAlgorithmDTO">
		update plugin.warehouse_algorithm
		set
		<if test="whseid != null and applyByNo !='' ">
			whseid = #{whseid, whseid=VARCHAR},
		</if>
		<if test="warehouse != null and warehouse !='' ">
			warehouse = #{warehouse, jdbcType=VARCHAR},
		</if>
		<if test="warehouseArea != null and warehouseArea !='' ">
			warehouse_area = #{warehouseArea, jdbcType=VARCHAR},
		</if>
		<if test="schemeType != null and schemeType !='' ">
			scheme_type = #{schemeType, jdbcType=VARCHAR},
		</if>
		<if test="algorithmScheme != null and algorithmScheme !='' ">
			algorithm_scheme = #{algorithmScheme, jdbcType=VARCHAR},
		</if>
		<if test="wholeWarehouseStrategy != null and wholeWarehouseStrategy !='' ">
			whole_warehouse_strategy = #{wholeWarehouseStrategy, jdbcType=VARCHAR},
		</if>
		<if test="maxWarehouseAdjust != null">
			max_warehouse_adjust = #{maxWarehouseAdjust, jdbcType=DECIMAL},
		</if>
		<if test="unitWholeWarehouseWork != null">
			unit_whole_warehouse_work = #{unitWholeWarehouseWork, jdbcType=DECIMAL},
		</if>
		<if test="startCoordX != null">
			start_coord_x = #{startCoordX, jdbcType=DECIMAL},
		</if>
		<if test="startCoordY != null">
			start_coord_y = #{startCoordY, jdbcType=DECIMAL},
		</if>
		<if test="endCoordX != null">
			end_coord_x = #{endCoordX, jdbcType=DECIMAL},
		</if>
		<if test="endCoordY != null">
			end_coord_y = #{endCoordY, jdbcType=DECIMAL},
		</if>
		<if test="executionFrequency != null">
			execution_frequency = #{executionFrequency, jdbcType=DECIMAL},
		</if>
		<if test="enabledFlag != null and enabledFlag !='' ">
			enabled_flag = #{enabledFlag, jdbcType=VARCHAR},
		</if>
		<if test="lastUpdatedBy != null and lastUpdatedBy !='' ">
			last_updated_by = #{lastUpdatedBy, jdbcType=VARCHAR},
		</if>
		last_update_date = sysdate
		where 1=1
		<if test="serialkey != null">
			and serialkey = #{serialkey,jdbcType=VARCHAR}
		</if>
		<if test="serialkey == null">
			and 1=2
		</if>
	</update>

	<select id="getWarehouseAlgorithmCount" parameterType="com.zte.interfaces.infor.dto.WarehouseAlgorithmDTO" resultType="java.lang.Integer">
		select count(1)
		from plugin.warehouse_algorithm t
		where 1 = 1
		<if test="list != null and list.size() > 0">
			and (whseid,warehouse,warehouse_area,algorithm_scheme) in
			<foreach collection="list" index="index" item="item" open="(" separator="," close=")">
				(#{item.whseid,jdbcType=VARCHAR},#{item.warehouse,jdbcType=VARCHAR},
				#{item.warehouseArea,jdbcType=VARCHAR},#{item.algorithmScheme,jdbcType=VARCHAR})
			</foreach>
		</if>
		and t.enabled_flag  = 'Y'
	</select>

	<select id="getAlgorithmResultHeadList" parameterType="com.zte.interfaces.infor.dto.AlgorithmResultDetailDTO" resultType="com.zte.interfaces.infor.dto.AlgorithmResultHeadDTO">
		select t.* from (
		select s.*, rownum rn from (
		select distinct
		arh.serialkey,
		arh.task_id taskId,
		arh.algorithm_scheme algorithmScheme,
		arh.expected_effect expectedEffect,
		arh.old_total_path oldTotalPath,
		arh.new_total_path newTotalPath,
		arh.expected_save_path expectedSavePath,
		arh.created_by createdBy,
		arh.creation_date creationDate,
		arh.whose_warehouse_distance wholeWarehouseDistance,
		slv.description status,
		arh.handle_progress handleProgress
		from plugin.algorithm_result_head arh
		join plugin.algorithm_result_detail ard
		on arh.task_id = ard.task_id
		left join plugin.sys_lookup_values slv
		on slv.lookup_type = '1000100' and slv.lookup_meaning = arh.status
		where
		arh.enabled_flag = 'Y'
		and ard.enabled_flag = 'Y'
		<if test="whseid != null and whseid !='' ">
			and ard.whseid  = #{whseid,jdbcType=VARCHAR}
		</if>
		<if test="warehouse != null and warehouse !='' ">
			and ard.warehouse  = #{warehouse,jdbcType=VARCHAR}
		</if>
		<if test="warehouseArea != null and warehouseArea !='' ">
			and ard.warehouse_area  = #{warehouseArea,jdbcType=VARCHAR}
		</if>
		<if test="algorithmScheme != null and algorithmScheme !='' ">
			and arh.algorithm_scheme = #{algorithmScheme,jdbcType=VARCHAR}
		</if>
		<if test="createdBy != null and createdBy !='' ">
			and arh.created_by  = #{createdBy,jdbcType=VARCHAR}
		</if>
		<if test="creationDateBegin != null and creationDateBegin !='' ">
			and arh.creation_date <![CDATA[>=]]> to_date(#{creationDateBegin,jdbcType=VARCHAR},'yyyy-mm-dd hh24:mi:ss')
		</if>
		<if test="creationDateEnd != null and creationDateEnd !='' ">
			and arh.creation_date <![CDATA[<=]]> to_date(#{creationDateEnd,jdbcType=VARCHAR},'yyyy-mm-dd hh24:mi:ss')
		</if>
		<if test="personId != null and personId !='' ">
			and ard.person_id  = #{personId,jdbcType=VARCHAR}
		</if>
		order by arh.serialkey
		) s ) t where 1=1
		<if test="startRow != null and  startRow != '' and endRow != null and endRow != ''  ">
			and  t.rn between #{startRow ,jdbcType=INTEGER} and #{endRow,jdbcType=INTEGER}
		</if>
		<if test="startRow == null or startRow == '' or endRow == null or endRow == ''  ">
			and  t.rn &lt;= 500000
		</if>
	</select>

	<select id="getAlgorithmResultHeadListVOTotal" parameterType="com.zte.interfaces.infor.dto.AlgorithmResultDetailDTO" resultType="java.lang.Integer">
		select count(1) from (
		select distinct arh.task_id
		from plugin.algorithm_result_head arh
		join plugin.algorithm_result_detail ard
		on arh.task_id = ard.task_id
		where
		arh.enabled_flag = 'Y'
		and ard.enabled_flag = 'Y'
		<if test="whseid != null and whseid !='' ">
			and ard.whseid  = #{whseid,jdbcType=VARCHAR}
		</if>
		<if test="warehouse != null and warehouse !='' ">
			and ard.warehouse  = #{warehouse,jdbcType=VARCHAR}
		</if>
		<if test="warehouseArea != null and warehouseArea !='' ">
			and ard.warehouse_area  = #{warehouseArea,jdbcType=VARCHAR}
		</if>
		<if test="algorithmScheme != null and algorithmScheme !='' ">
			and arh.algorithm_scheme = #{algorithmScheme,jdbcType=VARCHAR}
		</if>
		<if test="createdBy != null and createdBy !='' ">
			and arh.created_by  = #{createdBy,jdbcType=VARCHAR}
		</if>
		<if test="creationDateBegin != null and creationDateBegin !='' ">
			and arh.creation_date <![CDATA[>=]]> to_date(#{creationDateBegin,jdbcType=VARCHAR},'yyyy-mm-dd hh24:mi:ss')
		</if>
		<if test="creationDateEnd != null and creationDateEnd !='' ">
			and arh.creation_date <![CDATA[<=]]> to_date(#{creationDateEnd,jdbcType=VARCHAR},'yyyy-mm-dd hh24:mi:ss')
		</if>
		<if test="personId != null and personId !='' ">
			and ard.person_id  = #{personId,jdbcType=VARCHAR}
		</if>
		)
	</select>

	<select id="getAlgorithmResultDetailList" parameterType="com.zte.interfaces.infor.dto.AlgorithmResultDetailDTO" resultType="com.zte.interfaces.infor.dto.AlgorithmResultDetailDTO">
		select t.* from (
		select s.*,rownum rn from (
		select
		ard.task_id taskId,
		ard.sequence_id sequenceId,
		ard.algorithm_scheme algorithmScheme,
		ard.whseid,
		f.DESCRIPTION as whName,
		ard.warehouse,
		slv1.description warehouseDesc,
		ard.warehouse_area warehouseArea,
		slv2.description warehouseAreaDesc,
		ard.sku,
		ard.old_loc oldLoc,
		ard.new_loc newLoc,
		ard.created_by createdBy,
		ard.creation_date creationDate,
		ard.lpn lpn,
		slv3.description status,
		ard.person_id personId
		from plugin.algorithm_result_head arh
		join plugin.algorithm_result_detail ard
		on arh.task_id = ard.task_id
		left join ENTERPRISE.FACILITYNEST f
		on ard.whseid = upper(substr(f.name, 7))
		left join plugin.sys_lookup_values slv1
		on slv1.lookup_type = '1000064' and slv1.lookup_meaning = ard.warehouse
		left join plugin.sys_lookup_values slv2
		on slv2.lookup_type = '1000065' and slv2.lookup_meaning = ard.warehouse_area
		left join plugin.sys_lookup_values slv3
		on slv3.lookup_type = '1000100' and slv3.lookup_meaning = ard.status
		where
		arh.enabled_flag = 'Y'
		and ard.enabled_flag = 'Y'
		and slv1.enabled_flag = 'Y'
		and slv2.enabled_flag = 'Y'
		and slv3.enabled_flag = 'Y'
		<if test="taskId != null and taskId !='' ">
			and ard.task_id  = #{taskId,jdbcType=VARCHAR}
		</if>
		<if test="whseid != null and whseid !='' ">
			and ard.whseid  = #{whseid,jdbcType=VARCHAR}
		</if>
		<if test="warehouse != null and warehouse !='' ">
			and ard.warehouse  = #{warehouse,jdbcType=VARCHAR}
		</if>
		<if test="warehouseArea != null and warehouseArea !='' ">
			and ard.warehouse_area  = #{warehouseArea,jdbcType=VARCHAR}
		</if>
		<if test="algorithmScheme != null and algorithmScheme !='' ">
			and arh.algorithm_scheme = #{algorithmScheme,jdbcType=VARCHAR}
		</if>
		<if test="createdBy != null and createdBy !='' ">
			and arh.created_by  = #{createdBy,jdbcType=VARCHAR}
		</if>
		<if test="creationDateBegin != null and creationDateBegin !='' ">
			and arh.creation_date <![CDATA[>=]]> to_date(#{creationDateBegin,jdbcType=VARCHAR},'yyyy-mm-dd hh24:mi:ss')
		</if>
		<if test="creationDateEnd != null and creationDateEnd !='' ">
			and arh.creation_date <![CDATA[<=]]> to_date(#{creationDateEnd,jdbcType=VARCHAR},'yyyy-mm-dd hh24:mi:ss')
		</if>
		<if test="personId != null and personId !='' ">
			and ard.person_id  = #{personId,jdbcType=VARCHAR}
		</if>
		order by arh.serialkey,ard.sequence_id
		) s ) t where 1=1
		<if test="startRow != null and  startRow != '' and endRow != null and endRow != ''  ">
			and  t.rn between #{startRow ,jdbcType=INTEGER} and #{endRow,jdbcType=INTEGER}
		</if>
		<if test="startRow == null or startRow == '' or endRow == null or endRow == ''  ">
			and  t.rn &lt;= 500000
		</if>
	</select>

	<select id="getAlgorithmResultDetailListVOTotal" parameterType="com.zte.interfaces.infor.dto.AlgorithmResultDetailDTO" resultType="java.lang.Integer">
		select count(1)
		from plugin.algorithm_result_head arh
		join plugin.algorithm_result_detail ard
		on arh.task_id = ard.task_id
		where
		arh.enabled_flag = 'Y'
		and ard.enabled_flag = 'Y'
		<if test="taskId != null and taskId !='' ">
			and ard.task_id  = #{taskId,jdbcType=VARCHAR}
		</if>
		<if test="whseid != null and whseid !='' ">
			and ard.whseid  = #{whseid,jdbcType=VARCHAR}
		</if>
		<if test="warehouse != null and warehouse !='' ">
			and ard.warehouse  = #{warehouse,jdbcType=VARCHAR}
		</if>
		<if test="warehouseArea != null and warehouseArea !='' ">
			and ard.warehouse_area  = #{warehouseArea,jdbcType=VARCHAR}
		</if>
		<if test="algorithmScheme != null and algorithmScheme !='' ">
			and arh.algorithm_scheme = #{algorithmScheme,jdbcType=VARCHAR}
		</if>
		<if test="createdBy != null and createdBy !='' ">
			and arh.created_by  = #{createdBy,jdbcType=VARCHAR}
		</if>
		<if test="creationDateBegin != null and creationDateBegin !='' ">
			and arh.creation_date <![CDATA[>=]]> to_date(#{creationDateBegin,jdbcType=VARCHAR},'yyyy-mm-dd hh24:mi:ss')
		</if>
		<if test="creationDateEnd != null and creationDateEnd !='' ">
			and arh.creation_date <![CDATA[<=]]> to_date(#{creationDateEnd,jdbcType=VARCHAR},'yyyy-mm-dd hh24:mi:ss')
		</if>
		<if test="personId != null and personId !='' ">
			and ard.person_id  = #{personId,jdbcType=VARCHAR}
		</if>
	</select>

	<select id="getAlgorithmExecuteLogList" parameterType="com.zte.interfaces.infor.dto.AlgorithmExecuteLogDTO" resultType="com.zte.interfaces.infor.dto.AlgorithmExecuteLogDTO">
		select t.* from (
		select s.*,rownum rn from (
		select
		ael.task_id taskId,
		ael.algorithm_scheme algorithmScheme,
		ael.whseid,
		f.DESCRIPTION as whName,
		ael.scheme_type schemeType,
		ael.max_warehouse_adjust maxAdjustNum,
		ael.primal_distance primalDistance,
		ael.location_num locationNum,
		ael.pickdetail_num pickdetailNum,
		ael.valid_location_num validLocationNum,
		ael.valid_order_num validOrderNum,
		ael.iteration_num iterationNum,
		ael.operation_time operationTime,
		ael.optimization_proportion optimizationProportion,
		ael.remark,
		ael.created_by createdBy,
		ael.creation_date creationDate
		from plugin.algorithm_execute_log ael
		left join ENTERPRISE.FACILITYNEST f
		on ael.whseid = upper(substr(f.name, 7))
		where
		ael.enabled_flag = 'Y'
		<if test="taskId != null and taskId !='' ">
			and ael.task_id  = #{taskId,jdbcType=VARCHAR}
		</if>
		<if test="whseid != null and whseid !='' ">
			and ael.whseid  = #{whseid,jdbcType=VARCHAR}
		</if>
		<if test="algorithmScheme != null and algorithmScheme !='' ">
			and ael.algorithm_scheme = #{algorithmScheme,jdbcType=VARCHAR}
		</if>
		<if test="createdBy != null and createdBy !='' ">
			and ael.created_by  = #{createdBy,jdbcType=VARCHAR}
		</if>
		<if test="creationDateBegin != null and creationDateBegin !='' ">
			and ael.creation_date <![CDATA[>=]]> to_date(#{creationDateBegin,jdbcType=VARCHAR},'yyyy-mm-dd hh24:mi:ss')
		</if>
		<if test="creationDateEnd != null and creationDateEnd !='' ">
			and ael.creation_date <![CDATA[<=]]> to_date(#{creationDateEnd,jdbcType=VARCHAR},'yyyy-mm-dd hh24:mi:ss')
		</if>
		order by ael.serialkey
		) s ) t where 1=1
		<if test="startRow != null and  startRow != '' and endRow != null and endRow != ''  ">
			and  t.rn between #{startRow ,jdbcType=INTEGER} and #{endRow,jdbcType=INTEGER}
		</if>
		<if test="startRow == null or startRow == '' or endRow == null or endRow == ''  ">
			and  t.rn &lt;= 500000
		</if>
	</select>

	<select id="getAlgorithmExecuteLogListVOTotal" parameterType="com.zte.interfaces.infor.dto.AlgorithmExecuteLogDTO" resultType="java.lang.Integer">
		select count(1)
		from plugin.algorithm_execute_log ael
		where
		ael.enabled_flag = 'Y'
		<if test="taskId != null and taskId !='' ">
			and ael.task_id  = #{taskId,jdbcType=VARCHAR}
		</if>
		<if test="whseid != null and whseid !='' ">
			and ael.whseid  = #{whseid,jdbcType=VARCHAR}
		</if>
		<if test="algorithmScheme != null and algorithmScheme !='' ">
			and ael.algorithm_scheme = #{algorithmScheme,jdbcType=VARCHAR}
		</if>
		<if test="createdBy != null and createdBy !='' ">
			and ael.created_by  = #{createdBy,jdbcType=VARCHAR}
		</if>
		<if test="creationDateBegin != null and creationDateBegin !='' ">
			and ael.creation_date <![CDATA[>=]]> to_date(#{creationDateBegin,jdbcType=VARCHAR},'yyyy-mm-dd hh24:mi:ss')
		</if>
		<if test="creationDateEnd != null and creationDateEnd !='' ">
			and ael.creation_date <![CDATA[<=]]> to_date(#{creationDateEnd,jdbcType=VARCHAR},'yyyy-mm-dd hh24:mi:ss')
		</if>
	</select>
	<select id="getAlgorithmTaskDetailList" resultType="com.zte.interfaces.infor.dto.AlgorithmTaskDetailDTO">
		select t.* from (
		select s.*,rownum rn from (
		select
		art.task_id taskId,
		art.algorithm_scheme algorithmScheme,
		art.whseid,
		f.DESCRIPTION as whName,
		art.creation_date creationDate,
		art.person_id personId,
		slv1.description status,
		art.handle_progress handleProgress
		from plugin.algorithm_result_task art
		left join ENTERPRISE.FACILITYNEST f
		on art.whseid = upper(substr(f.name, 7))
		left join plugin.sys_lookup_values slv1
		on slv1.lookup_type = '1000100' and slv1.lookup_meaning = art.status
		where
		art.enabled_flag = 'Y'
		and slv1.enabled_flag = 'Y'
		<if test="taskId != null and taskId !='' ">
			and art.task_id  = #{taskId,jdbcType=VARCHAR}
		</if>
		<if test="algorithmScheme != null and algorithmScheme !='' ">
			and art.algorithm_scheme = #{algorithmScheme,jdbcType=VARCHAR}
		</if>
		order by art.serialkey desc
		) s ) t where 1=1
		<if test="startRow != null and  startRow != '' and endRow != null and endRow != ''  ">
			and  t.rn between #{startRow ,jdbcType=INTEGER} and #{endRow,jdbcType=INTEGER}
		</if>
		<if test="startRow == null or startRow == '' or endRow == null or endRow == ''  ">
			and  t.rn &lt;= 500000
		</if>
	</select>
	<select id="getAlgorithmTaskDetailListTotal" resultType="java.lang.Integer">
		select
		count(1)
		from plugin.algorithm_result_task art
		where
		art.enabled_flag = 'Y'
		<if test="taskId != null and taskId !='' ">
			and art.task_id  = #{taskId,jdbcType=VARCHAR}
		</if>
		<if test="algorithmScheme != null and algorithmScheme !='' ">
			and art.algorithm_scheme = #{algorithmScheme,jdbcType=VARCHAR}
		</if>
		order by art.serialkey
	</select>
	<select id="getAlgorithmTaskList" resultType="com.zte.interfaces.infor.dto.AlgorithmTaskDetailDTO">
		select
		ard.task_id taskId,
		ard.algorithm_scheme algorithmScheme,
		ard.whseid,
		f.DESCRIPTION as whName,
		arh.creation_date creationDate,
		ard.person_id personId
		from plugin.algorithm_result_head arh
		join plugin.algorithm_result_detail ard
		on arh.task_id = ard.task_id
		left join ENTERPRISE.FACILITYNEST f
		on ard.whseid = upper(substr(f.name, 7))
		where
		arh.enabled_flag = 'Y'
		and ard.enabled_flag = 'Y'
		<if test="taskId != null and taskId !='' ">
			and ard.task_id  = #{taskId,jdbcType=VARCHAR}
		</if>
		<if test="algorithmScheme != null and algorithmScheme !='' ">
			and arh.algorithm_scheme = #{algorithmScheme,jdbcType=VARCHAR}
		</if>
		group by ard.person_id,ard.algorithm_scheme,ard.task_id,ard.whseid,f.DESCRIPTION,arh.creation_date
	</select>
	<insert id="insertAlgorithmTaskDetailList">
		insert into plugin.ALGORITHM_RESULT_TASK
		(serialkey,TASK_ID,ALGORITHM_SCHEME,CREATION_DATE,PERSON_ID,WHSEID,STATUS,HANDLE_PROGRESS)
		select PLUGIN.SEQ_ALGORITHM_RESULT_TASK.nextval, temp.* from (
		<foreach collection="list" item="item" index="index" separator="union all">
			select
			#{item.taskId,jdbcType=VARCHAR},#{item.algorithmScheme,jdbcType=VARCHAR},to_date(#{item.creationDate,jdbcType=VARCHAR},'yyyy-MM-dd HH24:mi:ss'),
			#{item.personId,jdbcType=VARCHAR},#{item.whseid,jdbcType=VARCHAR},'0','0%'
			from dual
		</foreach>
		) temp
	</insert>
	<select id="getInforInventoryInfo" resultType="java.lang.String">
		select id
		from ${whseid}.lotxlocxid
		where sku = #{sku,jdbcType=VARCHAR}
		and loc = #{oldLoc,jdbcType=VARCHAR}
		and qty > 0
	</select>
	<delete id="deleteTaskDetailInfo" parameterType="com.zte.interfaces.infor.dto.AlgorithmResultDetailDTO">
		delete from plugin.algorithm_result_detail
		where task_id = #{taskId,jdbcType=VARCHAR}
		and sku = #{sku,jdbcType=VARCHAR}
		and old_loc = #{oldLoc,jdbcType=VARCHAR}
	</delete>
	<insert id="insertAlgorithmDetailList">
		insert into plugin.algorithm_result_detail (serialkey, task_id, sequence_id, whseid, sku, warehouse,
		warehouse_area, algorithm_scheme, old_loc, new_loc, enabled_flag, created_by, creation_date,
		last_updated_by, last_update_date, person_id, status, lpn)
		select plugin.algorithm_result_detail_s.nextval, temp.* from (
		<foreach collection="list" item="item" index="index" separator="union all">
			select
			upper(#{item.taskId, jdbcType=VARCHAR}),
			#{item.sequenceId,jdbcType=DECIMAL},
			#{item.whseid,jdbcType=VARCHAR},
			#{item.sku,jdbcType=VARCHAR},
			#{item.warehouse,jdbcType=VARCHAR},
			#{item.warehouseArea,jdbcType=VARCHAR},
			#{item.algorithmScheme,jdbcType=VARCHAR},
			#{item.oldLoc,jdbcType=VARCHAR},
			#{item.newLoc,jdbcType=VARCHAR},
			'Y',
			#{item.createdBy,jdbcType=VARCHAR},
			sysdate as creationDate,
			#{item.lastUpdatedBy,jdbcType=VARCHAR},
			sysdate as lastUpdateDate,
			#{item.personId,jdbcType=VARCHAR},
			'0',
			#{item.lpn,jdbcType=VARCHAR}
			from dual
		</foreach>
		) temp
	</insert>
</mapper>