<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.domain.model.step.ZteAlibabaRepository">

    <select id="getBillInfo" parameterType="com.zte.interfaces.step.dto.ZteDeductionPlanParamDTO" resultType="com.zte.interfaces.step.dto.ZteDeductionBillInfoDTO">
        <if test='billType == "1" '>
            select b.serial_id billId, b.bill_no billNo, c.customer_control_type customerControlType, (case when c.is_sn = 3 then c.is_sn else 0 end) isSn,
            b.status, b.trans_type transType, b.approval_type approvalType,b.description remark, d.serial_id detailId,
            d.in_qty qty, nvl(d.open_qty,0) openQty, c.customer_item_no mpn, b.customer_plan_id customerPlanId, b.origin_plan_id originPlanId,
            d.new_toid pkgId, d.item_barcode itemBarcode, 1 billType
            from kxstepiii.infor_scatter_bill b
            left join kxstepiii.infor_scatter_detail d
            on b.serial_id = d.head_id
            left join kxstepiii.ba_item_wms_v c
            on d.item_no = c.item_no
            where b.bill_no = #{billNo, jdbcType=VARCHAR}
            <if test="detailIdList != null and detailIdList.size() > 0">
                and d.serial_id in
                <foreach collection="detailIdList" item="item" index="index" open="(" separator="," close=")">
                    #{item, jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test='businessType == "2" or  businessType == "4" '>
                and d.approval_qty > 0
            </if>
        </if>
        <if test='billType == "2" '>
            select b.serial_id billId, b.bill_no billNo, c.customer_control_type customerControlType, (case when c.is_sn = 3 then c.is_sn else 0 end) isSn,
            b.status, b.trans_type transType, b.approval_type approvalType, b.commenttxt remark, d.serial_id detailId,
            d.out_qty qty, nvl(d.open_qty,0) openQty, c.customer_item_no mpn, b.customer_plan_id customerPlanId, b.origin_plan_id originPlanId,
            null pkgId, d.item_barcode itemBarcode, 2 billType
            from kxstepiii.infor_requisition_bill b
            left join kxstepiii.infor_requisition_detail d
            on b.serial_id = d.head_id
            left join kxstepiii.ba_item_wms_v c
            on d.item_no = c.item_no
            where b.bill_no = #{billNo, jdbcType=VARCHAR}
            <if test="detailIdList != null and detailIdList.size() > 0">
                and d.serial_id in
                <foreach collection="detailIdList" item="item" index="index" open="(" separator="," close=")">
                    #{item, jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test='businessType == "2" or  businessType == "4" '>
                and d.approval_qty > 0
            </if>
        </if>
        <if test='billType == "3" '>
            select b.bill_id billId, b.bill_no billNo, c.customer_control_type customerControlType, (case when c.is_sn = 3 then c.is_sn else 0 end) isSn,
            b.status, b.trans_type transType, b.approval_type approvalType, b.remark, d.detail_id detailId,
            d.plan_qty qty, nvl(d.open_qty,0) openQty, c.customer_item_no mpn, b.customer_plan_id customerPlanId, b.origin_plan_id originPlanId,
            d.new_toid pkgId, d.item_barcode itemBarcode, 3 billType
            from kxstepiii.infor_cm_return_bill b
            left join kxstepiii.infor_cm_return_detail d
            on b.bill_id = d.bill_id
            left join kxstepiii.ba_item i
            on d.item_id = i.item_id
            left join kxstepiii.ba_item_wms_v c
            on i.item_no = c.item_no
            where b.bill_no = #{billNo,jdbcType=VARCHAR}
            <if test="detailIdList != null and detailIdList.size() > 0">
                and d.detail_id in
                <foreach collection="detailIdList" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test='businessType == "2" or  businessType == "4" '>
                and d.approval_qty > 0
            </if>
        </if>
        <if test='billType == "4" '>
            select b.bill_id billId, b.bill_no billNo, c.customer_control_type customerControlType, (case when c.is_sn = 3 then c.is_sn else 0 end) isSn,
            b.status, b.trans_type transType, b.approval_type approvalType, b.remark, d.detail_id detailId,
            d.plan_qty qty, nvl(d.open_qty,0) openQty, c.customer_item_no mpn, b.customer_plan_id customerPlanId, b.origin_plan_id originPlanId,
            null pkgId, d.barcode itemBarcode, 4 billType
            from kxstepiii.infor_cm_requisition_bill b
            left join kxstepiii.infor_cm_requisition_detail d
            on b.bill_id = d.bill_id
            left join kxstepiii.ba_item_wms_v c
            on d.item_no = c.item_no
            where b.bill_no = #{billNo,jdbcType=VARCHAR}
            <if test="detailIdList != null and detailIdList.size() > 0">
                and d.detail_id in
                <foreach collection="detailIdList" item="item" index="index" open="(" separator="," close=")">
                    #{item, jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test='businessType == "2" or  businessType == "4" '>
                and d.approval_qty > 0
            </if>
        </if>
    </select>

    <update id="updateBillHead"  parameterType="com.zte.interfaces.step.dto.ZteDeductionPlanParamDTO">
        <if test='billType == "1" '>
            update kxstepiii.infor_scatter_bill b
            set b.status = #{status,jdbcType=VARCHAR},
            <if test="customerPlanId != null and customerPlanId != '' ">
                b.customer_plan_id = #{customerPlanId,jdbcType=VARCHAR},
            </if>
            b.last_updated_date = sysdate
            where b.bill_no = #{billNo,jdbcType=VARCHAR}
        </if>
        <if test='billType == "2" '>
            update kxstepiii.infor_requisition_bill b
            set b.status = #{status,jdbcType=VARCHAR},
            <if test="customerPlanId != null and customerPlanId != '' ">
                b.customer_plan_id = #{customerPlanId,jdbcType=VARCHAR},
            </if>
            b.last_updated_date = sysdate
            where b.bill_no = #{billNo,jdbcType=VARCHAR}
        </if>
        <if test='billType == "3" '>
            update kxstepiii.infor_cm_return_bill b
            set b.status = #{status,jdbcType=VARCHAR},
            <if test="customerPlanId != null and customerPlanId != '' ">
                b.customer_plan_id = #{customerPlanId,jdbcType=VARCHAR},
            </if>
            b.last_updated_date = sysdate
            where b.bill_no = #{billNo,jdbcType=VARCHAR}
        </if>
        <if test='billType == "4" '>
            update kxstepiii.infor_cm_requisition_bill b
            set b.status = #{status,jdbcType=VARCHAR},
            <if test="customerPlanId != null and customerPlanId != '' ">
                b.customer_plan_id = #{customerPlanId,jdbcType=VARCHAR},
            </if>
            b.last_updated_date = sysdate
            where b.bill_no = #{billNo,jdbcType=VARCHAR}
        </if>
    </update>

    <select id="getBillHeadByPlanId" parameterType="java.util.List" resultType="com.zte.interfaces.step.dto.ZteDeductionBillInfoDTO">
        select b.serial_id billId, b.bill_no billNo, b.customer_plan_id customerPlanId, '1' billType, approval_type approvalType
        from kxstepiii.infor_scatter_bill b
        where b.customer_plan_id in
        <foreach collection="list" item="item" index="index" open="(" close=")" separator=",">
            #{item,jdbcType=VARCHAR}
        </foreach>
        and b.status = 'APPROVALING'
        union all
        select b.serial_id billId, b.bill_no billNo, b.customer_plan_id customerPlanId, '2' billType, approval_type approvalType
        from kxstepiii.infor_requisition_bill b
        where b.customer_plan_id in
        <foreach collection="list" item="item" index="index" open="(" close=")" separator=",">
            #{item,jdbcType=VARCHAR}
        </foreach>
        and b.status = 'APPROVALING'
        union all
        select b.bill_id billId, b.bill_no billNo, b.customer_plan_id customerPlanId, '3' billType, approval_type approvalType
        from kxstepiii.infor_cm_return_bill b
        where b.customer_plan_id in
        <foreach collection="list" item="item" index="index" open="(" close=")" separator=",">
            #{item,jdbcType=VARCHAR}
        </foreach>
        and b.status = 'APPROVALING'
        union all
        select b.bill_id billId, b.bill_no billNo, b.customer_plan_id customerPlanId, '4' billType, approval_type approvalType
        from kxstepiii.infor_cm_requisition_bill b
        where b.customer_plan_id in
        <foreach collection="list" item="item" index="index" open="(" close=")" separator=",">
            #{item,jdbcType=VARCHAR}
        </foreach>
        and b.status = 'APPROVALING'
    </select>

    <update id="updateBillDetailApprovalQty"  parameterType="com.zte.interfaces.step.dto.ZteApproveResultDTO">
        <foreach collection="list" item="item" index="index"   separator=";" open="begin" close=";end;">
            <if test='item.billType == "1" '>
                update kxstepiii.infor_scatter_detail d
                set d.approval_qty = #{item.approveAvailableQuantity,jdbcType=INTEGER}
                where d.head_id = #{item.billId,jdbcType=INTEGER}
                and d.serial_id = #{item.detailId,jdbcType=INTEGER}
                and d.approval_qty is null
            </if>
            <if test='item.billType == "2" '>
                update kxstepiii.infor_requisition_detail d
                set d.approval_qty = #{item.approveAvailableQuantity,jdbcType=INTEGER}
                where d.head_id = #{item.billId,jdbcType=INTEGER}
                and d.serial_id = #{item.detailId,jdbcType=INTEGER}
                and d.approval_qty is null
            </if>
            <if test='item.billType == "3" '>
                update kxstepiii.infor_cm_return_detail d
                set d.approval_qty = #{item.approveAvailableQuantity,jdbcType=INTEGER}
                where d.bill_id = #{item.billId,jdbcType=INTEGER}
                and d.detail_id = #{item.detailId,jdbcType=INTEGER}
                and d.approval_qty is null
            </if>
            <if test='item.billType == "4" '>
                update kxstepiii.infor_cm_requisition_detail d
                set d.approval_qty = #{item.approveAvailableQuantity,jdbcType=INTEGER}
                where d.bill_id = #{item.billId,jdbcType=INTEGER}
                and d.detail_id = #{item.detailId,jdbcType=INTEGER}
                and d.approval_qty is null
            </if>
        </foreach>
    </update>

    <update id="updateBillDetailOpenQty"  parameterType="com.zte.interfaces.step.dto.ZteDeductionBillInfoDTO">
        <foreach collection="list" item="item" index="index" separator=";" open="begin" close=";end;">
            <if test='item.billType == "1" '>
                update kxstepiii.infor_scatter_detail d
                set d.open_qty = nvl(d.open_qty, 0) + #{item.openingQty,jdbcType=INTEGER}
                where d.head_id = #{item.billId,jdbcType=INTEGER}
                and d.serial_id = #{item.detailId,jdbcType=INTEGER}
            </if>
            <if test='item.billType == "2" '>
                update kxstepiii.infor_requisition_detail d
                set d.open_qty = nvl(d.open_qty, 0) + #{item.openingQty,jdbcType=INTEGER}
                where d.head_id = #{item.billId,jdbcType=INTEGER}
                and d.serial_id = #{item.detailId,jdbcType=INTEGER}
            </if>
            <if test='item.billType == "3" '>
                update kxstepiii.infor_cm_return_detail d
                set d.open_qty = nvl(d.open_qty, 0) + #{item.openingQty,jdbcType=INTEGER}
                where d.bill_id = #{item.billId,jdbcType=INTEGER}
                and d.detail_id = #{item.detailId,jdbcType=INTEGER}
            </if>
            <if test='item.billType == "4" '>
                update kxstepiii.infor_cm_requisition_detail d
                set d.open_qty = nvl(d.open_qty, 0) + #{item.openingQty,jdbcType=INTEGER}
                where d.bill_id = #{item.billId,jdbcType=INTEGER}
                and d.detail_id = #{item.detailId,jdbcType=INTEGER}
            </if>
        </foreach>
    </update>

    <select id="getBillDetailNotApproved" parameterType="com.zte.interfaces.step.dto.ZteApproveResultDTO" resultType="java.lang.Integer">
        <if test='billType == "1" '>
            select count(1) from kxstepiii.infor_scatter_detail d
            where d.head_id = #{billId,jdbcType=INTEGER}
            and d.approval_qty is null
        </if>
        <if test='billType == "2" '>
            select count(1) from kxstepiii.infor_requisition_detail d
            where d.head_id = #{billId,jdbcType=INTEGER}
            and d.approval_qty is null
        </if>
        <if test='billType == "3" '>
            select count(1) from kxstepiii.infor_cm_return_detail d
            where d.bill_id = #{billId,jdbcType=INTEGER}
            and d.approval_qty is null
        </if>
        <if test='billType == "4" '>
            select count(1) from kxstepiii.infor_cm_requisition_detail d
            where d.bill_id = #{billId,jdbcType=INTEGER}
            and d.approval_qty is null
        </if>
    </select>

    <insert id="insertZmsNoticeApproveResult" parameterType="com.zte.interfaces.step.dto.ZteApproveResultDTO">
        INSERT INTO kxstepiii.zms_notice_approve_result
        (serial_id, plan_id, deduction_item_line_number, approve_status, approver, approve_desc, gmt_approved,
        approve_total_quantity, approve_available_quantity, next_flow_node, created_by, last_updated_by)
        select kxstepiii.zms_notice_approve_result_s.nextval, temp.* from (
        <foreach collection="list" item="item" index="index" separator="union all">
            select
            #{item.planId,jdbcType=VARCHAR} planId, #{item.deductionItemLineNumber,jdbcType=VARCHAR} deductionItemLineNumber,
            #{item.approveStatus,jdbcType=INTEGER} approveStatus, #{item.approver,jdbcType=VARCHAR} approver,
            #{item.approveDesc,jdbcType=VARCHAR} approveDesc, #{item.gmtApproved,jdbcType=INTEGER} gmtApproved,
            #{item.approveTotalQuantity,jdbcType=INTEGER} approveTotalQuantity,
            #{item.approveAvailableQuantity,jdbcType=INTEGER} approveAvailableQuantity,
            #{item.nextFlowNode,jdbcType=VARCHAR} nextFlowNode,
            'system' createdBy, 'system' lastUpdatedBy
            from dual
        </foreach>
        ) temp
    </insert>

    <select id="getControlItemNo" parameterType="java.util.List" resultType="com.zte.interfaces.step.dto.ItemNoMpnDTO">
        select s.item_no itemNo,s.CUSTOMER_ITEM_NO mpn,(case when s.is_sn = 3 then s.is_sn else 0 end) isSn
        from kxstepiii.ba_item_wms_v s
        <where>
            customer_control_type = 1
            <if test="itemNos != null and itemNos.size() > 0">
                and s.item_no in
                <foreach collection="itemNos" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="itemNos == null or itemNos.size() == 0">
                and 1=2
            </if>
        </where>
    </select>

    <select id="selectPicklistMain" resultType="com.zte.interfaces.step.dto.ZmsPicklistMainDTO"
            parameterType="com.zte.interfaces.step.dto.ZmsPicklistMainInDTO">
        SELECT T.ENTITY_NAME AS TASK_NO,
               '' AS TASK_TYPE,
               T.BILL_NO AS BILL_NUMBER,
               T.STATUS AS BILL_STATUS,
               '' AS PRODUCT_ADDRESS,
               T.WAREHOURSE_ID AS SUB_STOCK,
               '维修' AS PICK_TYPE
          FROM KXSTEPIII.INFOR_CM_REQUISITION_BILL T
        <where>
            <if test="taskNos != null and taskNos.size() >0">
                AND T.ENTITY_NAME IN
                <foreach collection="taskNos" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="startDate != null and startDate != '' and endDate != null and endDate != ''">
                AND T.LAST_UPDATED_DATE BETWEEN
                TO_DATE(#{startDate,jdbcType=VARCHAR}, 'YYYY-MM-DD HH24:MI:SS') AND
                TO_DATE(#{endDate,jdbcType=VARCHAR}, 'YYYY-MM-DD HH24:MI:SS')
            </if>
            <if test="(taskNos == null or taskNos.size() == 0) and
                    (startDate == null or startDate == '' or endDate == null or endDate == '')">
                AND 1=2
            </if>
        </where>
    </select>

    <select id="getInforScatterSnDTOList" parameterType="com.zte.interfaces.step.dto.ZteDeductionBillInfoDTO" resultType="com.zte.interfaces.step.dto.InforScatterSnDTO">
        select distinct t.bill_no billNo, t.pkg_id pkgId, t.origin_bill_no originBillNo, t.origin_plan_id originPlanId,
        t.origin_detail_id originDetailId
        from kxstepiii.infor_scatter_sn t
        where t.bill_no = #{billNo, jdbcType=VARCHAR}
        and t.pkg_id = #{pkgId, jdbcType=VARCHAR}
    </select>

    <select id="getInforApprovalAlibabaList" parameterType="java.lang.String" resultType="com.zte.interfaces.step.dto.InforApprovalAlibabaDTO">
        select t.bill_no billNo, t.detail_id detailId, t.item_no itemNo, t.item_barcode itemBarcode, t.in_qty inQty,
        t.plan_id planId, t.deduction_item_line_number deductionItemLineNumber, t.origin_bill_no originBillNo,
        t.origin_serialkey originSerialkey, t.origin_detail_id originDetailId, t.origin_plan_id originPlanId,
        t.apply_qty applyQty, t.approval_qty approvalQty
        from kxstepiii.infor_approval_alibaba t
        where t.bill_no = #{billNo, jdbcType=VARCHAR}
        and t.status = 1
    </select>

    <select id="getInforEdiSoAlibabaList" parameterType="java.lang.String" resultType="com.zte.interfaces.step.dto.InforEdiSoAlibabaDTO">
        select m.serialkey,m.externalorderkey2,m.externlineno,m.sku,m.lottable02,m.shippedqty,m.approvalqty,
        m.originPlanId,m.shippedqty-m.approvalqty-m.approvalingqty canUseQty
        from (
        select iea.serialkey,iea.externalorderkey2,iea.externlineno,iea.sku,iea.lottable02,iea.shippedqty,iea.approvalqty,
        irb.customer_plan_id originPlanId,nvl(sum(iaa.apply_qty),0) approvalingqty
        from kxstepiii.infor_edi_so_alibaba iea
        join kxstepiii.infor_requisition_bill irb
        on irb.bill_no = iea.externalorderkey2
        left join kxstepiii.infor_approval_alibaba iaa
        on iea.externalorderkey2 = iaa.origin_bill_no
        and iea.externlineno = iaa.origin_detail_id
        and iaa.status = 0
        where iea.lottable02 = #{itemBarcode, jdbcType=VARCHAR}
        and iea.shippedqty - iea.approvalqty > 0
        group by iea.serialkey,iea.externalorderkey2,iea.externlineno,iea.sku,iea.lottable02,iea.shippedqty,iea.approvalqty,irb.customer_plan_id
        union
        select iea.serialkey,iea.externalorderkey2,iea.externlineno,iea.sku,iea.lottable02,iea.shippedqty,iea.approvalqty,
        icb.customer_plan_id originPlanId,nvl(sum(iaa.apply_qty),0) approvalingqty
        from kxstepiii.infor_edi_so_alibaba iea
        join kxstepiii.infor_cm_requisition_bill icb
        on icb.bill_no = iea.externalorderkey2
        left join kxstepiii.infor_approval_alibaba iaa
        on iea.externalorderkey2 = iaa.origin_bill_no
        and iea.externlineno = iaa.origin_detail_id
        and iaa.status = 0
        where iea.lottable02 = #{itemBarcode, jdbcType=VARCHAR}
        and iea.shippedqty - iea.approvalqty > 0
        group by iea.serialkey,iea.externalorderkey2,iea.externlineno,iea.sku,iea.lottable02,iea.shippedqty,iea.approvalqty,icb.customer_plan_id
        ) m
        where m.shippedqty-m.approvalqty-m.approvalingqty > 0
        order by m.serialkey desc
    </select>

    <insert id="insertInforApprovalAlibaba" parameterType="com.zte.interfaces.step.dto.InforApprovalAlibabaDTO">
        INSERT INTO kxstepiii.infor_approval_alibaba
        (serial_id, bill_no, detail_id, item_no, item_barcode, in_qty, plan_id, deduction_item_line_number,
        origin_bill_no, origin_serialkey, origin_detail_id, origin_plan_id, apply_qty, created_by)
        select kxstepiii.infor_approval_alibaba_s.nextval, temp.* from (
        <foreach collection="list" item="item" index="index" separator="union all">
            select
            #{item.billNo,jdbcType=VARCHAR} billNo, #{item.detailId,jdbcType=VARCHAR} detailId,
            #{item.itemNo,jdbcType=VARCHAR} itemNo, #{item.itemBarcode,jdbcType=VARCHAR} itemBarcode,
            #{item.inQty,jdbcType=INTEGER} inQty, #{item.planId,jdbcType=VARCHAR} planId,
            #{item.deductionItemLineNumber,jdbcType=VARCHAR} deductionItemLineNumber,
            #{item.originBillNo,jdbcType=VARCHAR} originBillNo,
            #{item.originSerialkey,jdbcType=VARCHAR} originSerialkey,
            #{item.originDetailId,jdbcType=VARCHAR} originDetailId,
            #{item.originPlanId,jdbcType=VARCHAR} originPlanId,
            #{item.applyQty,jdbcType=INTEGER} applyQty,
            #{item.createdBy,jdbcType=VARCHAR} createdBy
            from dual
        </foreach>
        ) temp
    </insert>

    <delete id="deleteInforApprovalAlibaba" parameterType="java.lang.String">
        delete from kxstepiii.infor_approval_alibaba t
        where t.bill_no = #{billNo, jdbcType=VARCHAR}
        and t.status = 0
    </delete>

    <update id="updateInforApprovalAlibaba" parameterType="com.zte.interfaces.step.dto.ZteApproveResultDTO">
        <foreach collection="list" item="item" index="index"   separator=";" open="begin" close=";end;">
            update kxstepiii.infor_approval_alibaba
            <set>
                approval_qty = nvl(approval_qty,0) + #{item.approveAvailableQuantity, jdbcType=INTEGER},
                last_updated_by = 'B2B',
                last_update_date = sysdate,
                status = 1
            </set>
            where plan_id = #{item.planId,jdbcType=VARCHAR}
            and deduction_item_line_number = #{item.deductionItemLineNumber,jdbcType=VARCHAR}
            and status = 0
        </foreach>
    </update>

    <update id="updateInforEdiSoAlibaba" parameterType="com.zte.interfaces.step.dto.InforEdiSoAlibabaDTO">
        <foreach collection="list" item="item" index="index"   separator=";" open="begin" close=";end;">
            update kxstepiii.infor_edi_so_alibaba
            <set>
                approvalqty = nvl(approvalqty,0) + #{item.approvalQty, jdbcType=INTEGER},
                last_updated_by = 'B2B',
                last_update_date = sysdate
            </set>
            where serialkey = #{item.serialkey,jdbcType=VARCHAR}
        </foreach>
    </update>

    <select id="getLookupValues" parameterType="com.zte.interfaces.step.dto.StepSysLookupValuesDTO" resultType="com.zte.interfaces.step.dto.StepSysLookupValuesDTO">
        select b.*
        from kxstepiii.sys_lookup_types a
        join kxstepiii.sys_lookup_values b
        on a.lookup_type = b.lookup_type
        and b.enabled_flag = 'Y'
        where a.enabled_flag = 'Y'
        <if test="lookupType != null and lookupType !='' ">
            and a.lookup_type = #{lookupType, jdbcType=VARCHAR}
        </if>
        <if test="lookupMeaning != null and lookupMeaning !='' ">
            and b.lookup_meaning = #{lookupMeaning, jdbcType=VARCHAR}
        </if>
    </select>

    <select id="getUnSplitBoxReceiveQty" resultType="com.zte.interfaces.infor.dto.AliOrderSubmitDetailDTO">
        select t.qtyexpected qty, T.TOID boxNo from
        kxstepiii.infor_edi_po t
        where t.href11 in ('100','110','120','130','140')
        and  t.TOID in
        <foreach collection="boxNos" item="boxNo" separator=","  open="("  close=")">
            #{boxNo, jdbcType=VARCHAR}
        </foreach>
    </select>
</mapper>