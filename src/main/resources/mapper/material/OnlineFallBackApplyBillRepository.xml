<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.domain.model.material.OnlineFallBackApplyBillRepository">
    <resultMap id="FallbackApplyBillMap" type="com.zte.domain.model.material.StFallbackApplybillHead">
        <result column="fallback_no" jdbcType="VARCHAR" property="fallbackNo" />
        <result column="nonconforming_product_no" jdbcType="VARCHAR" property="nonconformingProductNo" />
        <result column="fallback_type" jdbcType="VARCHAR" property="type" />
        <result column="supplier_no" jdbcType="VARCHAR" property="supplierNo" />
        <result column="supplier_name" jdbcType="VARCHAR" property="supplierName" />
        <result column="return_remark" jdbcType="VARCHAR" property="returnRemark" />
        <result column="target_address" jdbcType="VARCHAR" property="targetAddress" />
        <result column="return_user_id" jdbcType="VARCHAR" property="returnUserId" />
        <result column="return_user_name" jdbcType="VARCHAR" property="returnUserName" />
        <result column="org_id" jdbcType="VARCHAR" property="orgId" />
        <result column="href45" jdbcType="VARCHAR" property="href45" />
        <result column="lottable06" jdbcType="VARCHAR" property="lottable06" />
        <result column="target_location" jdbcType="VARCHAR" property="targetLocation" />
    </resultMap>
	<select id="queryFallbackHead" parameterType="java.lang.String" resultMap="FallbackApplyBillMap">
		select h.fallback_no,h.nonconforming_product_no,h.fallback_type,h.supplier_no,h.supplier_name,h.return_remark,h.target_address,
		h.return_user_id,h.return_user_name,h.org_id,p.href45,p.lottable06,UPPER(h.target_location) target_location
		from st_fallback_head   h
		join st_fallback_detail d
		on h.fallback_head_guid = d.fallback_head_guid
		left join st_fallback_product p
		on p.nonconforming_product_no=h.nonconforming_product_no
		where UPPER(h.enabled_flag) = 'Y' and h.fallback_no = #{fallbackNo,jdbcType=VARCHAR}
		and d.item_barcode = p.item_barcode
		AND h.fall_status = 'WAREHOUSED'
  	</select>

    <resultMap id="FallbackApplyBillDetailMap" type="com.zte.domain.model.material.StFallbackApplybillDetail">
        <result column="fallback_detail_guid" jdbcType="VARCHAR" property="fallbackDetailguid" />
        <result column="return_qty" jdbcType="DECIMAL" property="qty" />
        <result column="pkg_id" jdbcType="VARCHAR" property="toId" />
        <result column="item_barcode" jdbcType="VARCHAR" property="itemBarcode" />
    </resultMap>
	<select id="queryFallbackDetail" parameterType="java.lang.String" resultMap="FallbackApplyBillDetailMap">
		select fallback_detail_guid,return_qty,pkg_id,item_barcode from st_fallback_detail
		where UPPER(enabled_flag) = 'Y' and fallback_no = #{fallbackNo,jdbcType=VARCHAR}
  	</select>

  	<insert id="insertApplyBillNoLog" parameterType="java.lang.String">
  		insert into st_fallback_applybill_log (applyBillNo,sendTimes,isSend)
  		select #{applybillNo ,jdbcType=VARCHAR},1,0
  		from dual
  		where not exists ( select 1 from st_fallback_applybill_log where applyBillNo = #{applybillNo ,jdbcType=VARCHAR})
  	</insert>

  	<select id="queryWaitPushList" resultType="java.lang.String">
  	    select applyBillNo from st_fallback_applybill_log where isSend = 0 and <![CDATA[sendTimes < 6]]>
  	</select>

  	<update id="updateApplyBillNoLog">
  		update st_fallback_applybill_log
  		set isSend = #{flag,jdbcType=INTEGER},
  		sendTimes = sendTimes + 1
  		where applyBillNo = #{applyBillNo ,jdbcType=VARCHAR}
  	</update>

    <update id="syncRealOutNumber">
        update st_fallback_applybill_detail
        set real_out_number = #{realOutNumber,jdbcType=DOUBLE}
        where applybill_no = #{applyBillNo ,jdbcType=VARCHAR}
        and row_no = #{rowNo,jdbcType=INTEGER}
    </update>

    <insert id="insertStFallbackApplybillHead" parameterType="com.zte.domain.model.material.StFallbackApplybillHead">
    	insert into st_fallback_applybill_head (applybill_no,fallback_no,nonconforming_product_no,type,status,status_history,deli_no,
    	supplier_no,supplier_name,return_remark,target_address,take_user,take_tel,return_user_id,return_user_name,
    	org_id,create_by,create_date,last_updated_by,last_updated_date,enabled_flag,href45,lottable06,iwms_status)
        values (
        #{applybillNo ,jdbcType=VARCHAR},
        #{fallbackNo ,jdbcType=VARCHAR},
        #{nonconformingProductNo ,jdbcType=VARCHAR},
        #{type ,jdbcType=VARCHAR},
        #{status ,jdbcType=VARCHAR},
        #{statusHistory ,jdbcType=VARCHAR},
		#{deliNo ,jdbcType=VARCHAR},
        #{supplierNo ,jdbcType=VARCHAR},
        #{supplierName ,jdbcType=VARCHAR},
        #{returnRemark ,jdbcType=VARCHAR},
		#{targetAddress ,jdbcType=VARCHAR},
		#{takeUser ,jdbcType=VARCHAR},
		#{takeTel ,jdbcType=VARCHAR},
		#{returnUserId ,jdbcType=VARCHAR},
		#{returnUserName ,jdbcType=VARCHAR},
		#{orgId ,jdbcType=VARCHAR},
		#{createBy ,jdbcType=VARCHAR},
		str_to_date(#{createDate ,jdbcType=VARCHAR}, '%Y-%m-%d %H:%i:%s'),
		#{lastUpdatedBy ,jdbcType=VARCHAR},
		str_to_date(#{lastUpdatedDate ,jdbcType=VARCHAR}, '%Y-%m-%d %H:%i:%s'),
        #{enabledFlag ,jdbcType=VARCHAR},
        #{href45 ,jdbcType=VARCHAR},
        #{lottable06 ,jdbcType=VARCHAR},
        #{iwmsStatus ,jdbcType=VARCHAR}
        )
	</insert>


	<insert id="insertStFallbackApplybillDetail" parameterType="com.zte.domain.model.material.StFallbackApplybillDetail" >
		insert into st_fallback_applybill_detail(fallback_applybill_guid,fallback_detail_guid,applybill_no,row_no,deli_row_no,out_no,out_row_no,
			create_by,create_date,last_updated_by,last_updated_date,enabled_flag,to_id,qty,item_barcode)
		values
		<foreach collection="listDetail" item="item" index="index" separator=",">
		(
            uuid(),#{item.fallbackDetailguid ,jdbcType=VARCHAR},
            #{item.applybillno ,jdbcType=VARCHAR},
            #{item.rowno ,jdbcType=INTEGER},
            #{item.deliRowNo ,jdbcType=VARCHAR},
            #{item.outNo ,jdbcType=VARCHAR},
            #{item.outRowNo ,jdbcType=INTEGER},
            #{item.createBy ,jdbcType=VARCHAR},
            str_to_date(#{item.createDate ,jdbcType=VARCHAR}, '%Y-%m-%d %H:%i:%s'),
            #{item.lastUpdatedBy ,jdbcType=VARCHAR},
            str_to_date(#{item.lastUpdatedDate ,jdbcType=VARCHAR}, '%Y-%m-%d %H:%i:%s'),
            #{item.enabledFlag ,jdbcType=VARCHAR},
            #{item.toId ,jdbcType=VARCHAR},
            #{item.qty ,jdbcType=INTEGER},
            #{item.itemBarcode ,jdbcType=VARCHAR}
		)
		</foreach>
	</insert>

    <resultMap id="ApplyBillDTOMap" type="com.zte.interfaces.onlinefallback.dto.ApplyBillDTO">
        <result column="origId" jdbcType="INTEGER" property="origId" />
        <result column="billNo" jdbcType="VARCHAR" property="billNo" />
        <result column="returnType" jdbcType="VARCHAR" property="returnType" />
        <result column="supCode" jdbcType="VARCHAR" property="supCode" />
        <result column="supName" jdbcType="VARCHAR" property="supName" />
        <result column="deliNo" jdbcType="VARCHAR" property="deliNo" />
        <result column="nonconformityNo" jdbcType="VARCHAR" property="nonconformityNo" />
        <result column="returnReason" jdbcType="VARCHAR" property="returnReason" />
        <result column="createUser" jdbcType="VARCHAR" property="createUser" />
        <result column="takeGoodsSite" jdbcType="VARCHAR" property="takeGoodsSite" />
        <result column="takeGoodsPhone" jdbcType="VARCHAR" property="takeGoodsPhone" />
        <result column="createTime" jdbcType="VARCHAR" property="createTime" />
        <result column="cargoOwner" jdbcType="VARCHAR" property="cargoOwner" />
    </resultMap>
	<select id="queryFallbackApplybillHead" parameterType="java.lang.String" resultMap="ApplyBillDTOMap">
	select id origId,applybill_no billNo,type returnType,
		supplier_no supCode,supplier_name supName,deli_no deliNo,
		nonconforming_product_no nonconformityNo,return_remark returnReason,
		return_user_id createUser,target_address takeGoodsSite,take_tel takeGoodsPhone,
		'' createTime,lottable06 cargoOwner
	from st_fallback_applybill_head
	where applybill_no = #{applybillNo,jdbcType=VARCHAR} and UPPER(enabled_flag) = 'Y' and status = 'NEW'
  	</select>


    <resultMap id="ApplyBillDetailMap" type="com.zte.interfaces.onlinefallback.dto.ApplyBillItemDTO">
        <result column="rowNo" jdbcType="INTEGER" property="rowNo" />
        <result column="itemNo" jdbcType="VARCHAR" property="itemNo" />
        <result column="returnQty" jdbcType="DOUBLE" property="returnQty" />
        <result column="barCode" jdbcType="VARCHAR" property="barCode" />
        <result column="deliRowNo" jdbcType="VARCHAR" property="deliRowNo" />
        <result column="produceBatchNo" jdbcType="VARCHAR" property="produceBatchNo" />
        <result column="importBatchNo" jdbcType="VARCHAR" property="importBatchNo" />
        <result column="outNo" jdbcType="VARCHAR" property="outNo" />
        <result column="outRowNo" jdbcType="INTEGER" property="outRowNo" />
    </resultMap>
	<select id="queryFallbackApplybillDetail" parameterType="java.lang.String" resultMap="ApplyBillDetailMap">
	select sfad.row_no rowNo,sfd.item_no itemNo,sfad.qty returnQty,
		sfd.item_barcode barCode,sfad.deli_row_no deliRowNo,sfd.prodplan_id produceBatchNo,
		sfd.import_batch_id importBatchNo,sfad.out_no outNo ,sfad.out_row_no outRowNo
	from st_fallback_applybill_detail sfad
		LEFT JOIN st_fallback_detail sfd on sfd.fallback_detail_guid = sfad.fallback_detail_guid
	where sfad.applybill_no = #{applybillNo,jdbcType=VARCHAR} and upper(sfad.enabled_flag) = 'Y' and upper(sfd.enabled_flag) = 'Y'
  	</select>

    <select id="queryStFallbackApplybillHeadHandlingOptions" parameterType="java.lang.String" resultType="java.lang.String">
	    select sfah.handling_opinions
	    from st_fallback_applybill_head sfah
	    where 1=1
        <if test="applybillNo != null and applybillNo != ''">
            and applybill_no = #{applybillNo,jdbcType=VARCHAR}
        </if>
         and upper(sfah.enabled_flag) = 'Y'
  	</select>

	<update id="updateStFallbackApplybillHead" parameterType="com.zte.domain.model.material.StFallbackApplybillHead">
		update st_fallback_applybill_head
		<set>
			<if test="status != null and status != '' and status != 'string'">
				status = #{status, jdbcType=VARCHAR},
				status_history = concat(IFNULL(status_history,'-'),',',IFNULL(#{status, jdbcType=VARCHAR},'-')),
			</if>
			<if test="submitInforStatus != null and submitInforStatus != '' and submitInforStatus != 'string'">
				submit_infor_status = #{submitInforStatus, jdbcType=VARCHAR},
			</if>
			<if test="lastUpdatedBy != null and lastUpdatedBy != '' and lastUpdatedBy != 'string'">
				last_updated_by = #{lastUpdatedBy, jdbcType=VARCHAR},
			</if>
			<if test="submitError != null and submitError != '' and submitError != 'string'">
				submit_error = #{submitError, jdbcType=VARCHAR},
			</if>
			<if test="modifyTime != null and modifyTime != '' and modifyTime != 'string'">
				modify_time = str_to_date(#{modifyTime, jdbcType=VARCHAR}, '%Y-%m-%d %H:%i:%s'),
			</if>
			<if test="returnComments != null and returnComments != '' and returnComments != 'string'">
				return_comments = #{returnComments, jdbcType=VARCHAR},
			</if>
			<if test="returnModel != null and returnModel != '' and returnModel != 'string'">
				return_model = #{returnModel, jdbcType=VARCHAR},
			</if>
			<if test="handlingOpinions != null and handlingOpinions != '' and handlingOpinions != 'string'">
				handling_opinions = #{handlingOpinions, jdbcType=VARCHAR},
			</if>
			<if test="scrappingReason != null and scrappingReason != '' and scrappingReason != 'string'">
				scrapping_reason = #{scrappingReason, jdbcType=VARCHAR},
			</if>
			<if test="remark != null and remark != '' and remark != 'string'">
				remark = #{remark, jdbcType=VARCHAR},
			</if>
			<if test="pickStyle != null and pickStyle != '' and pickStyle != 'string'">
				pick_style = #{pickStyle, jdbcType=VARCHAR},
			</if>
			<if test="receiver != null and receiver != '' and receiver != 'string'">
				receiver = #{receiver, jdbcType=VARCHAR},
			</if>
			<if test="receiverContactNo != null and receiverContactNo != '' and receiverContactNo != 'string'">
				receiver_contactno = #{receiverContactNo, jdbcType=VARCHAR},
			</if>
			<if test="receiverAddress != null and receiverAddress != '' and receiverAddress != 'string'">
				receiver_address = #{receiverAddress, jdbcType=VARCHAR},
			</if>
			<if test="carrier != null and carrier != '' and carrier != 'string'">
				carrier = #{carrier, jdbcType=VARCHAR},
			</if>
			<if test="returnAddress != null and returnAddress != '' and returnAddress != 'string'">
				return_address = #{returnAddress, jdbcType=VARCHAR},
			</if>
			<if test="pickAddress != null and pickAddress != '' and pickAddress != 'string'">
				pick_address = #{pickAddress, jdbcType=VARCHAR},
			</if>
			<if test="processCount != null and processCount !=0 ">
				process_count = process_count+1,
			</if>
            <if test="closeRemark != null and closeRemark != '' and closeRemark != 'string'">
                close_remark = #{closeRemark, jdbcType=VARCHAR},
            </if>
            <if test="iwmsStatus != null and iwmsStatus !=''">
                iwms_status = #{iwmsStatus,jdbcType=VARCHAR},
            </if>
			enabled_flag = 'Y',
			last_updated_date = str_to_date(#{lastUpdatedDate, jdbcType=VARCHAR}, '%Y-%m-%d %H:%i:%s')
		</set>
		where applybill_no = #{applybillNo, jdbcType=VARCHAR} and upper(enabled_flag) = 'Y'
	</update>

    <resultMap id="ApplyBillSoResultDTOMap" type="com.zte.interfaces.onlinefallback.dto.ApplyBillSoResultDTO">
        <result column="origId" jdbcType="INTEGER" property="origId" />
        <result column="billNo" jdbcType="VARCHAR" property="billNo" />
        <result column="returnType" jdbcType="VARCHAR" property="returnType" />
        <result column="fallbackNo" jdbcType="VARCHAR" property="fallbackNo" />
        <result column="handlingOpinions" jdbcType="VARCHAR" property="handlingOpinions" />
    </resultMap>
	<select id="queryApplyBillSoResultHead" parameterType="java.lang.String" resultMap="ApplyBillSoResultDTOMap">
	select id origId,applybill_no billNo,type returnType,fallback_no fallbackNo,handling_opinions handlingOpinions
	from st_fallback_applybill_head
	where applybill_no = #{applybillNo,jdbcType=VARCHAR} and UPPER(enabled_flag) = 'Y'
  	</select>

    <resultMap id="ApplyBillSoResultItemDTOMap" type="com.zte.interfaces.onlinefallback.dto.ApplyBillSoResultItemDTO">
        <result column="rowNo" jdbcType="INTEGER" property="rowNo" />
        <result column="barCode" jdbcType="VARCHAR" property="barCode" />
        <result column="lpn" jdbcType="VARCHAR" property="lpn" />
        <result column="exParkNumber" jdbcType="VARCHAR" property="exParkNumber" />
        <result column="realOutNumber" jdbcType="DOUBLE" property="realOutNumber" />
        <result column="length" jdbcType="VARCHAR" property="length" />
        <result column="width" jdbcType="VARCHAR" property="width" />
        <result column="high" jdbcType="VARCHAR" property="high" />
        <result column="grossWeight" jdbcType="VARCHAR" property="grossWeight" />
    </resultMap>
	<select id="queryApplyBillSoResultDetail" parameterType="java.lang.String" resultMap="ApplyBillSoResultItemDTOMap">
	select sfad.row_no rowNo,sfd.item_barcode barCode,ifnull(sfad.to_id,concat(IFNULL(sfd.pkg_id,' '),'|',IFNULL(sfd.nonconforming_product_no,' '))) lpn, '' exParkNumber, IFNULL(sfad.qty,sfd.return_qty) realOutNumber,
	cast(sfd.box_len as char) length, cast(sfd.box_width as char) width, cast(sfd.box_height as char) high,
	cast(sfd.box_weight as char) grossWeight
	from st_fallback_applybill_detail sfad
	LEFT JOIN st_fallback_detail sfd on sfd.fallback_detail_guid = sfad.fallback_detail_guid
	where sfad.applybill_no = #{applybillNo,jdbcType=VARCHAR} and upper(sfad.enabled_flag) = 'Y' and upper(sfd.enabled_flag) = 'Y'
  	</select>

	<select id="queryApplyBillSoResultReelSn" parameterType="java.lang.String" resultType="java.util.HashMap" >
	select distinct concat(IFNULL(sfd.pkg_id,' '),'|',IFNULL(sfd.nonconforming_product_no,' ')) rowNo,sfpr.reelid reelSn
	from st_fallback_product_reelid sfpr
	LEFT JOIN st_fallback_detail sfd on sfd.nonconforming_product_no = sfpr.nonconforming_product_no and sfd.pkg_id = sfpr.pkg_id
	LEFT JOIN st_fallback_applybill_detail sfad on sfd.fallback_detail_guid = sfad.fallback_detail_guid
	where sfad.applybill_no = #{applybillNo,jdbcType=VARCHAR} and upper(sfpr.enabled_flag) = 'Y' and upper(sfd.enabled_flag) = 'Y'
	and upper(sfpr.enabled_flag) = 'Y'
  	</select>

    <resultMap id="FallbackNoInfoMap" type="com.zte.interfaces.onlinefallback.dto.FallbackNoInfo">
        <result column="fallback_no" jdbcType="VARCHAR" property="fallbackNo" />
        <result column="fallback_type" jdbcType="VARCHAR" property="fallbackType" />
        <result column="fall_status" jdbcType="VARCHAR" property="fallStatus" />
        <result column="source_location" jdbcType="VARCHAR" property="sourcelocation" />
        <result column="target_location" jdbcType="VARCHAR" property="targetLocation" />
    </resultMap>

	<select id="queryWaitFallback" parameterType="com.zte.interfaces.onlinefallback.dto.FallbackNoInfo" resultMap="FallbackNoInfoMap">
		select distinct fallback_no,fallback_type,fall_status,UPPER(source_location) source_location,UPPER(target_location) target_location
		from st_fallback_head where UPPER(enabled_flag) = 'Y'
		<if test="index != null">
			and (fall_status = 'STOCK_OUT' or (fall_status = 'SUBMITED' and fallback_type = '01')) and target_location != ''
		</if>
		<if test="index == null">
			and fall_status = 'SUBMITED' and fallback_type = '02' and source_location != ''
		</if>
		<if test="fallbackNo != null and fallbackNo != ''">
			and fallback_no = #{fallbackNo,jdbcType=VARCHAR}
		</if>
  	</select>

      <select id="getWsdlUrl" resultType="String" >
        SELECT lookup_meaning FROM st_sys_lookup_values sslv
        LEFT JOIN st_sys_lookup_types sslt ON sslv.lookup_type = sslt.lookup_type
        WHERE sslt.enabled_flag='Y' AND sslv.sort_seq=0 AND sslt.meaning='URL_STOREMESSAGE'
    </select>

	<update id="updateFallbackStatus" parameterType="com.zte.interfaces.onlinefallback.dto.FallbackNoInfo">
		update st_fallback_head
		<set>
			fall_status = #{fallStatus, jdbcType=VARCHAR},
			last_updated_by = #{lastUpdatedBy, jdbcType=VARCHAR},
			last_updated_date = str_to_date(#{lastUpdatedDate, jdbcType=VARCHAR}, '%Y-%m-%d %H:%i:%s')
		</set>
		<where>
			fallback_no = #{fallbackNo, jdbcType=VARCHAR}
		</where>
	</update>

	<update id="updateBatchFallbackStatus" parameterType="java.util.List">
		<foreach collection="list" item="info" index="index" open="" close="" separator =";" >
			update st_fallback_head
			<set>
				fall_status = #{info.fallStatus, jdbcType=VARCHAR},
				last_updated_by = #{info.lastUpdatedBy, jdbcType=VARCHAR},
				last_updated_date = str_to_date(#{info.lastUpdatedDate, jdbcType=VARCHAR}, '%Y-%m-%d %H:%i:%s')
			</set>
			<where>
			fallback_no = #{info.fallbackNo, jdbcType=VARCHAR}
			</where>
		</foreach>
	</update>

	<resultMap id="SoHeaderMap" type="com.zte.resourcewarehouse.common.utils.inforclient.entity.SoHeader">
      <result column="orderKey" jdbcType="VARCHAR" property="orderKey" />
      <result column="externOrderKey" jdbcType="VARCHAR" property="externOrderKey" />
      <result column="type" jdbcType="VARCHAR" property="type" />
      <result column="storerKey" jdbcType="VARCHAR" property="storerKey" />
      <result column="requestedShipDate" jdbcType="VARCHAR" property="requestedShipDate" />
      <result column="externalOrderKey2" jdbcType="VARCHAR" property="externalOrderKey2" />
      <result column="consigneeKey" jdbcType="VARCHAR" property="consigneeKey" />
      <result column="ordersId" jdbcType="VARCHAR" property="ordersId" />
      <result column="hsusr1" jdbcType="VARCHAR" property="hsusr1" />
      <result column="hsusr2" jdbcType="VARCHAR" property="hsusr2" />
      <result column="hsusr3" jdbcType="VARCHAR" property="hsusr3" />
      <result column="hsusr4" jdbcType="VARCHAR" property="hsusr4" />
      <result column="hsusr5" jdbcType="VARCHAR" property="hsusr5" />
      <result column="href01" jdbcType="VARCHAR" property="href01" />
      <result column="href02" jdbcType="VARCHAR" property="href02" />
      <result column="href03" jdbcType="VARCHAR" property="href03" />
      <result column="href04" jdbcType="VARCHAR" property="href04" />
      <result column="href05" jdbcType="VARCHAR" property="href05" />
      <result column="href06" jdbcType="VARCHAR" property="href06" />
      <result column="href07" jdbcType="VARCHAR" property="href07" />
      <result column="href08" jdbcType="VARCHAR" property="href08" />
      <result column="href09" jdbcType="VARCHAR" property="href09" />
      <result column="href10" jdbcType="VARCHAR" property="href10" />
      <result column="href11" jdbcType="VARCHAR" property="href11" />
    </resultMap>

    <select id="getSoHeader" resultMap="SoHeaderMap" parameterType="java.lang.String">
        SELECT
        '' orderKey,
        sfah.fallback_no externOrderKey,
        '0' type,
        'ZTE' storerKey,
        #{params.curTime, jdbcType=VARCHAR} requestedShipDate,
        sfah.applybill_no externalOrderKey2,
        '' consigneeKey,
        '' ordersId,
        '' hsusr1,
        '' hsusr2,
        '' hsusr3,
        '' hsusr4,
        '' hsusr5,
        '' href01,
        '' href02,
        '' href03,
        '' href04,
        '' href05,
        '' href06,
        '' href07,
        '' href08,
        '' href09,
        '' href10,
        (select v.description_chin from st_sys_lookup_values v where v.lookup_type='1000032'
        and v.lookup_meaning=UPPER(sfah.href45)) href11
        FROM st_fallback_applybill_head sfah
        WHERE sfah.applybill_no = #{applybillNo, jdbcType=VARCHAR}
        and UPPER(sfah.enabled_flag) = 'Y'
    </select>

    <resultMap id="SoDetailMap" type="com.zte.resourcewarehouse.common.utils.inforclient.entity.SoDetail">
      <result column="orderLineNumber" jdbcType="VARCHAR" property="orderLineNumber" />
      <result column="externLineNo" jdbcType="VARCHAR" property="externLineNo" />
      <result column="whseid" jdbcType="VARCHAR" property="whseid" />
      <result column="sku" jdbcType="VARCHAR" property="sku" />
      <result column="packKey" jdbcType="VARCHAR" property="packKey" />
      <result column="originalQty" jdbcType="VARCHAR" property="originalQty" />
      <result column="shippedQty" jdbcType="VARCHAR" property="shippedQty" />
      <result column="uom" jdbcType="VARCHAR" property="uom" />
      <result column="allowOverPick" jdbcType="VARCHAR" property="allowOverPick" />
      <result column="preAllocateStrategyKey" jdbcType="VARCHAR" property="preAllocateStrategyKey" />
      <result column="allocateStrategyKey" jdbcType="VARCHAR" property="allocateStrategyKey" />
      <result column="allocateStrategyType" jdbcType="VARCHAR" property="allocateStrategyType" />
      <result column="shelfLife" jdbcType="VARCHAR" property="shelfLife" />
      <result column="rotation" jdbcType="VARCHAR" property="rotation" />
      <result column="skuRotation" jdbcType="VARCHAR" property="skuRotation" />
      <result column="lottable01" jdbcType="VARCHAR" property="lottable01" />
      <result column="lottable02" jdbcType="VARCHAR" property="lottable02" />
      <result column="lottable03" jdbcType="VARCHAR" property="lottable03" />
      <result column="lottable06" jdbcType="VARCHAR" property="lottable06" />
    </resultMap>

    <select id="getSoDetail" resultMap="SoDetailMap" parameterType="Map">
        SELECT
        '' orderLineNumber,
        sfad.row_no externLineNo,
        UPPER(sff.target_location) whseid,
        sfd.item_no sku,
        '' packKey,
        ifnull(sfad.qty,sfd.return_qty) originalQty,
        '0' shippedQty,
        '' uom,
        '0' allowOverPick,
        '' preAllocateStrategyKey,
        '' allocateStrategyKey,
        '' allocateStrategyType,
        '' shelfLife,
        '' rotation,
        '' skuRotation,
        '' lottable01,
        sfd.item_barcode lottable02,
        sfah.org_id lottable03,
        sfd.lottable06 lottable06
        FROM st_fallback_applybill_detail sfad
        LEFT JOIN st_fallback_applybill_head sfah on sfad.applybill_no = sfah.applybill_no
		LEFT JOIN st_fallback_detail sfd on sfd.fallback_detail_guid = sfad.fallback_detail_guid
		LEFT JOIN st_fallback_head sff on sff.fallback_head_guid = sfd.fallback_head_guid
		where sfad.applybill_no = #{fallbackNo,jdbcType=VARCHAR} and upper(sfad.enabled_flag) = 'Y'
    </select>

	<select id="queryWaitFeedApplyNo" parameterType="java.lang.String" resultMap="ApplyResultMap">
	select distinct st.applybill_no billNo,UPPER(f.target_location) target_location from st_fallback_applybill_head st
    join st_fallback_head f on st.fallback_no = f.fallback_no
	where UPPER(st.enabled_flag) = 'Y' and UPPER(f.enabled_flag) = 'Y' and (st.submit_infor_status = 'SUBMIT_SO' or st.status = 'PurWaitReturn')
	<if test="applybillNo != null and applybillNo != ''">
		and st.applybill_no = #{applybillNo,jdbcType=VARCHAR}
	</if>
    and st.process_count <![CDATA[<]]> 4
  	</select>

    <resultMap id="ApplyResultMap" type="com.zte.interfaces.onlinefallback.dto.ApplyBillDTO">
        <result column="billNo" jdbcType="VARCHAR" property="billNo" />
        <result column="reelid" jdbcType="VARCHAR" property="reelid" />
        <result column="reelid_flag" jdbcType="DECIMAL" property="reelidFlag" />
        <result column="target_location" jdbcType="VARCHAR" property="targetLocation" />
    </resultMap>

    <select id="queryStFallbackDetailBarcode" parameterType="java.lang.String" resultMap="ApplyResultMap">
        SELECT sfah.applybill_no billNo, sfpr.reelid , sfp.reelid_flag from st_fallback_applybill_head sfah
        LEFT JOIN st_fallback_product_reelid sfpr
        on sfpr.nonconforming_product_no = sfah.nonconforming_product_no
        LEFT JOIN st_fallback_product sfp
        on sfpr.nonconforming_product_no = sfp.nonconforming_product_no
        where UPPER(sfah.enabled_flag) = 'Y' and UPPER(sfpr.enabled_flag) = 'Y' and UPPER(sfp.enabled_flag) = 'Y'
        and sfah.applybill_no IN
        <foreach collection="splitList" item="tmp" index="index" open="(" close=")" separator=",">
            #{tmp}
        </foreach>
    </select>

    <resultMap id="SysLookupValuesMap" type="com.zte.interfaces.onlinefallback.dto.StSysLookupValuesDTO">
        <result column="lookup_code" jdbcType="DECIMAL" property="lookupCode" />
        <result column="lookup_meaning" jdbcType="VARCHAR" property="lookupMeaning" />
        <result column="description_chin" jdbcType="VARCHAR" property="descriptionChin" />
        <result column="description_eng" jdbcType="VARCHAR" property="descriptionEng" />
        <result column="lookup_type" jdbcType="DECIMAL" property="lookupType" />
        <result column="editable_flag" jdbcType="VARCHAR" property="editableFlag" />
        <result column="sort_seq" jdbcType="DECIMAL" property="sortSeq" />
        <result column="remark" jdbcType="VARCHAR" property="remark" />
        <result column="create_by" jdbcType="VARCHAR" property="createBy" />
        <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
        <result column="last_updated_by" jdbcType="VARCHAR" property="lastUpdatedBy" />
        <result column="last_updated_date" jdbcType="TIMESTAMP" property="lastUpdatedDate" />
        <result column="enabled_flag" jdbcType="VARCHAR" property="enabledFlag" />
        <result column="org_id" jdbcType="DECIMAL" property="orgId" />
        <result column="factory_id" jdbcType="DECIMAL" property="factoryId" />
        <result column="entity_id" jdbcType="DECIMAL" property="entityId" />
    </resultMap>
    <select id="getSysLookupValues" parameterType="java.lang.String" resultMap="SysLookupValuesMap">
        select s.lookup_code,s.lookup_meaning
        from st_sys_lookup_values s
        join st_sys_lookup_types t
        on s.lookup_type = t.lookup_type
        where s.enabled_flag = 'Y'
        and t.enabled_flag = 'Y'
        and t.lookup_type = #{lookupType,jdbcType=DECIMAL}
    </select>
    <select id="getEccnSysLookupValues" parameterType="java.lang.String" resultMap="SysLookupValuesMap">
        select s.lookup_code,s.lookup_meaning,s.description_eng
        from st_sys_lookup_values s
        join st_sys_lookup_types t
        on s.lookup_type = t.lookup_type
        where s.enabled_flag = 'Y'
        and t.enabled_flag = 'Y'
        and t.lookup_type = #{lookupType,jdbcType=DECIMAL}
        <if test="lookupCode != null and lookupCode != ''">
            and s.lookup_code = #{lookupCode,jdbcType=DECIMAL}
        </if>
    </select>
    <select id="getSysLookupValuesByOrder" parameterType="java.lang.String" resultMap="SysLookupValuesMap">
        select s.lookup_code,s.lookup_meaning,s.description_chin
        from st_sys_lookup_values s
        join st_sys_lookup_types t
        on s.lookup_type = t.lookup_type
        where s.enabled_flag = 'Y'
        and t.enabled_flag = 'Y'
        and t.lookup_type = #{lookupType,jdbcType=DECIMAL}
        order by sort_seq
    </select>

    <update id="updateStFallbackProductByNonconformingProductNo" parameterType="com.zte.domain.model.material.StFallbackApplybillHead">
        update st_fallback_product
        set lottable06 = #{lottable06,jdbcType=VARCHAR}
        where nonconforming_product_no = #{nonconformingProductNo,jdbcType=VARCHAR}
    </update>

    <update id="updateLastUpdatedTime" parameterType="java.lang.String" >
        update st_sys_lookup_values
        set lookup_meaning = #{maxLastUpdatedTime,jdbcType=VARCHAR}
        where lookup_code = #{lookupCode,jdbcType=DECIMAL}
    </update>

    <select id="selectApplybillNoAndStatus" resultType="long">
        select count(1) from st_fallback_applybill_head
        where applybill_no = #{billNo}
          and status = #{strExcclosed}
          and enabled_flag = 'Y'
    </select>
</mapper>


