RetCode.Success=操作成功
RetCode.ServerError=服务器错误
RetCode.AuthFailed=认证失败
RetCode.PermissionDenied=没有权限
RetCode.ValidationError=验证失败
RetCode.BusinessError=业务异常

whseid.can.not.be.empty=whseid不能为空，请输入
invoke.success=调用成功
invoke.failed=调用失败
updata.success=更新成功
no.data.found=没有查询到数据
sync.eccn.lookupcode.not.config=ECCN同步数据字典未配置
sku.reelid.wmwhse.is.not.empty=仓库不能为空
sku.reelid.wmwhse.is.not.val1=仓库不符合规则,例如WMWHSE1
sku.reelid.wmwhse.is.not.val2=仓库只能输入WMWHSE1-WMWHSE50
sku.reelid.sku.is.not.empty=物料代码不能为空
sku.reelid.sku.is.not.val1=物料代码不能超过

infor.fallback.price.no.data=数据不存在，或者不是退货单
infor.fallback.price.fallback.no.empty=退货单号不能为空
infor.fallback.price.fallback.detail=明细不能为空
infor.fallback.price.detail.not.val1=第{0}行主键不能为空
infor.fallback.price.detail.not.val2=第{0}行外部行不能为空
infor.fallback.price.detail.not.val3=第{0}行条码不能为空
infor.fallback.price.detail.not.val4=第{0}行数量不能小于等于0
infor.fallback.price.detail.not.val5=第{0}行币种不能为空
infor.fallback.price.detail.not.val6=第{0}行价格不能小于等于0
infor.fallback.price.detail.not.val7=第{0}行无税价不能小于等于0
infor.fallback.price.detail.not.val8=第{0}行单价不能小于等于0
infor.fallback.price.detail.not.val9=第{0}行无税单价不能小于等于0
infor.fallback.price.detail.not.val10=第{0}行汇率不能小于等于0
infor.fallback.price.detail.not.val11=第{0}行税率不能小于等于0
infor.fallback.price.is.exist=价格已经存在，不允许修改价格
infor.fallback.price.no.sample.with.infor=数据与infor不一致，请检查
infor.fallback.price.query.no.data=查询不到数据，请检查
infor.fallback.price.data.can.not.sample=数量不一致，请检查
infor.fallback.applybill.not.exist=退库申请单不存在
infor.fallback.syslookup.values.not.exist=数据字典1000034不存在

infor.vmi.price.billNo.can.not.empty = 出库单单号不能为空
infor.vmi.price.serailKey.can.not.empty = 出库单行号不能为空
infor.vmi.price.exlineNo.can.not.empty = 行号不能为空
infor.vmi.price.itemBarcode.can.not.empty = 22条码不能为空
infor.vmi.price.currencyType.can.not.empty = 币种不能为空
infor.vmi.price.uniPrice.can.not.empty = 原币单价（含税）不能小于等于0
infor.vmi.price.uniPriceNoTax.can.not.empty = 原币单价(无税)不能小于等于0
infor.vmi.price.price.can.not.empty = 本币单价（含税）不能小于等于0
infor.vmi.price.priceNoTax.can.not.empty = 本币单价（无税）不能小于等于0
infor.vmi.price.exchangeRate.can.not.empty = 汇率不能小于等于0
infor.vmi.price.taxRate.can.not.empty = 税率不能小于等于0
infor.vmi.price.has.updated = {0}价格已刷新，不允许再次刷新
infor.vmi.price.not.existed = {0}不存在
infor.vmi.price.data.can.not.empty = 数据为空

infor.dispense.serial.whseid.val=仓库未激活
infor.dispense.serial.receipt=已经生成接收数据，禁止插入

Nonconforming.info.001 = 非ISCP交货，请按原流程退货！
Nonconforming.info.002 = 该不合格单的退货数量校验不通过！
Nonconforming.info.003 = 该不合格单没有要退货的条码！
Nonconforming.info.004 = 不合格品单不存在！
Nonconforming.info.005 = 不合格品单状态不正确！
Nonconforming.info.006 = 不合格品单没有经过物料技术经理处理录入条码，请确认！
infor.no.wait.feed.applyNo = infor没有等待退货的申请单
pushHZBillToStorageCenter.info.001 = token值或者员工短工号不能为空
barcode.center.validation.registerBarcode.empty=registerBarcode?containerBarcode/batchBarcode??????
barcode.center.validation.response.format=?????????????


infor.vmi.invquery.itemNo.cannot.empty = 物料代码不能为空
infor.vmi.invquery.itemNo.supplyNo.must.enter.one=物料代码和供应商代码必须填写其中一个
infor.so.sodetail.query.cannot.empty=未输入时间时,物料代码、单号、批次和LFID必须输入其中一个
infor.so.sodetail.query.error.dateformat=错误的时间格式
infor.so.edisosdetail.query.cannot.empty=外部单号不能为空
infor.controller.error.dateformat = 错误的时间格式

enter.at.least.one.query.condition=至少要输入一个查询条件
dropid.and.produceno.not.empty=落放ID不能为空
input.data.can.not.null = 输入的对象不能为空
can.not.be.greater.than.five.hundred = 不能大于500
this.method.had.been.running = 此api后台正在执行，请稍后再试
can.not.get.key.value = 查无此key
please.input.key.value = 请输入key值
data.update.error = 数据更新出错
recheckno.can.not.be.empty=复检单号不能为空
recheckno.not.exists=复检单号不存在
recheckno.not.need.to.check=复检单不需要送检
recheckno.no.details=根据复检单号未找到单据明细
sys.lookup.values.1000037.not.exist=数据字典1000037不存在
date.formate.error = 日期格式错误

call.back.data.empty = 回传的对象为空
bill.no.empty = 业务单据号为空
oms.sale.bill.not.exists = OMS销售领料单不存在
iwms.qcbad.return.apply.bill.not.exists = iWMS退货申请单不存在
oms.sale.ecss.lookup.not.exists = OMS销售订单对接ECSS数据字典没有维护
can.not.be.greater.than.fifty = 不能大于50，请确认
hold.reason.not.sample.hold = 冻结原因不为试样超采或库存抽检，请确认
inventory.hold.record.exists = 库存冻结记录已存在，请确认
item.barcode.not.exists = 物料条码不存在，请确认
ba.item.not.exists = 材料代码不存在，请确认
ba.bom.head.not.exists = 单板代码不存在，请确认
choose.inventory.hold.record.effected = 选择的库存冻结记录已生效，请确认
choose.inventory.hold.record.expired = 选择的库存冻结记录已失效,请确认
please.login.first= 会话已失效，请先登录！
remark.too.long = 备注长度不能超过60
can.not.be.greater.than.five.thousand = 不能大于5000，请确认
sku.domestic.internal.enable.update.by.empty = 物料代码、境内后进先出、境外后进先出、是否有效、更新人不能为空
sku.can.not.repeat = 物料代码不能重复
stockNo.itemBarcode.check.count=step库位与物料条码最多支持500个
no.params=参数缺失
stockNo.check=没有查询到infor库位
itembarcode.repeat.check=220条码存在重复数据
require.repeat.import = 已导入，请稍后再试
require.repeat.factory.item = 工厂+代码不能重复，请核实后重新导入
require.item.not.exists = 导入的物料代码不存在，请核实后重新导入
factory.sku.domestic.qty.empty = 工厂、物料代码、数量不能为空
require.item.length.fail = 代码位数不符要求
check.inforloc=infor库位码不存在
push.b2b.failed = 推送b2b失败
get.order.query.for.infor.failed = 调用采购接口orderQueryForInfor失败
get.return.order.query.for.infor.failed = 调用采购接口returnOrderQueryForInfor失败
inventory.hold.approvedby.empty = 请选择审批人
failed_to_process_approval_center_kafka_message = 处理审批中心kafka消息失败
item.qte.stqe.not.exists = 请先维护TQE和STQE
iqc.test.requisition.detail.not.val1=箱明细数据不能为空
iqc.test.requisition.detail.not.val2 = 送货单+条码不能重复领料
item.no.not.exists = 物料代码不存在，请确认
iqc.test.requisition.reel.not.null = REEL数据为空
iqc.test.requisition.stock.error = 仓库编码有误
iqc.test.requisition.query.param.is.null = 送货单号、出库单号、物料代码、物料条码为空，则起始时间必须输入，且时间跨度<2年
infor.loc.not.exists = 位置码不存在，请确认
warehouse.road.work.exists = 路网数据已存在，请确认
warehouse.road.work.repeated= 路网数据重复，请确认
warehouse.algorithm.exists = 算法方案已存在，请确认
warehouse.algorithm.repeated = 算法方案重复，请确认
algorithm.scheme.type.inconsistent.exists = 方案类型与已维护的算法方案不一致
algorithm.whole.warehouse.strategy.inconsistent.exists = 整库策略与已维护的算法方案不一致
algorithm.max.warehouse.adjust.inconsistent.exists = 最大调整库位数与已维护的算法方案不一致
algorithm.unit.whole.warehouse.work.inconsistent.exists = 单位整库工作量与已维护的算法方案不一致
algorithm.start.coord.x.inconsistent.exists = 起点x坐标与已维护的算法方案不一致
algorithm.start.coord.y.inconsistent.exists = 起点y坐标与已维护的算法方案不一致
algorithm.end.coord.x.inconsistent.exists = 终点x坐标与已维护的算法方案不一致
algorithm.end.coord.y.inconsistent.exists = 终点y坐标与已维护的算法方案不一致
algorithm.execution.frequency.inconsistent.exists = 执行频率与已维护的算法方案不一致
algorithm.scheme.type.inconsistent = 导入算法方案的方案类型不一致
algorithm.whole.warehouse.strategy.inconsistent = 导入算法方案的整库策略不一致
algorithm.max.warehouse.adjust.inconsistent = 导入算法方案的最大调整库位数不一致
algorithm.unit.whole.warehouse.work.inconsistent = 导入算法方案的单位整库工作量不一致
algorithm.start.coord.x.inconsistent = 导入算法方案的起点x坐标不一致
algorithm.start.coord.y.inconsistent = 导入算法方案的起点y坐标不一致
algorithm.end.coord.x.inconsistent = 导入算法方案的终点x坐标不一致
algorithm.end.coord.y.inconsistent = 导入算法方案的终点y坐标不一致
algorithm.execution.frequency.inconsistent = 导入算法方案的执行频率不一致
delivery.no.received.not.exists = 送货单不存在
delivery.no.received.repeat = 送货单已存在签到记录，请勿重复签到
productbase.no.data.whse = 请维护基地对应的仓库
transfer.stock.receive.for.infor.failed = 调用采购接口transferStockReceiveForInfor失败
eccn.version.eccn.for.iwms.failed = 调用ECCN接口versionEccn失败
red.dot.status.check = infor出库任务红点状态已更新！
red.dot.execute.date.check = 执行间隔小于{0}分钟！
msl.wmwhse.is.not.empty = 仓库不能为空
msl.sku.is.not.empty = 物料代码不能为空
msl.lottable02.is.not.empty = 物料条码不能为空
msl.serialnumber.is.not.empty = reelid不能为空
can.not.find.road = 当前查询条件查不到对应的路网信息
can.not.find.replenishment  = 当前查询条件查不到补货信息
using.algorithm.error = 调用算法的接口有误.
inone.osp.state.query.error = osp状态查询失败
itemNo.beginDate.endDate.is.not.empty = 物料代码、起始时间、终止时间不能为空
quantity.greater.limit = 物料代码数量不能超过限定值
source.system.empty = 来源系统不能为空
business.type.empty = 业务类型不能为空
bill.type.empty= 单据类型不能为空
bill.not.exists = 单据不存在
bill.status.not.submited = 单据状态不为已提交，不允许提交申请
bill.status.not.recheck = 单据状态不为待复核，不允许提交申请
bill.status.not.ecss.audited = 单据状态不为ECSS已审核，不允许提交申请
customer.control.type.not.alibaba = 客户管控类型不为阿里管控
syslookup.values.not.exists = 数据字典不存在
query.whseid.error = 查询INFOR仓库号异常
bill.sn.not.bounded = 单据的SN未完成绑定
query.infor.inventory.error = 查询INFOR库存数据异常
query.repair.inventory.error = 查询标准仓维修库库存数据异常
merge.inventory.error = 汇总库存数据异常
merge.inventory.null = 汇总数据为空
upload.inventory.error = 上传库存数据异常
query.warehouse.error = 查询仓库信息失败
date.not.empty = 日期不能为空
hold.reason.not.spot.check = 冻结原因不为库存抽检，请确认
details.are.already.submitted.cannot.submit=存在已提交的明细行，不能提交
details.are.already.submitted.cannot.delete=存在已提交的明细行，不能删除
details.is.null=提交明细为空
have.submitted=机型{0}正在提交中，请稍等。
params.limit=输入数据超过500条限制
transfer.bill.not.exists=调拨单不存在
transfer.bill.status.not.make=调拨单状态不为已下达，不允许送检
transfer.bill.stock.not.proofing=调拨单源仓不为研发打样库，不允许送检
transfer.bill.send.iqc.failed=调拨单送检失败
check.result.detail.not.exists=检验结果明细为空
transfer.bill.status.not.testing=调拨单状态不为检验中，不允许回传检验结果
transfer.detail.status.not.testing=调拨单明细状态不为检验中，不允许回传检验结果
algorithm.execution.manpower.inconsistent=导入算法方案的整库人力不一致
algorithm.execution.unit.inconsistent=导入算法方案的最小整库单元不一致