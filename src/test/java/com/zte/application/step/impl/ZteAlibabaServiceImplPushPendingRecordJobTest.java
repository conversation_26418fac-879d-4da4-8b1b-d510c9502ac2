package com.zte.application.step.impl;

import com.zte.domain.model.infor.*;
import com.zte.domain.model.step.ZteStockInfoUploadRepository;
import com.zte.interfaces.infor.dto.*;
import com.zte.interfaces.step.dto.*;
import com.zte.resourcewarehouse.common.utils.BusiAssertException;
import com.zte.resourcewarehouse.common.utils.RedisSerialNoUtil;
import com.zte.common.utils.ImesCenterfactoryRemoteService;
import com.zte.itp.msa.core.exception.BusiException;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.math.BigDecimal;
import java.util.*;

import static com.zte.common.utils.Constant.*;
import static com.zte.common.utils.NumConstant.*;
import static com.zte.common.utils.NumConstant.INT_0;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * ZteAlibabaServiceImpl pushPendingRecordJob方法的单元测试
 * 分支覆盖率100%
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({RedisSerialNoUtil.class, BusiAssertException.class, BusiException.class})
public class ZteAlibabaServiceImplPushPendingRecordJobTest {

    @InjectMocks
    private ZteAlibabaServiceImpl zteAlibabaService;

    @Mock
    private InventoryholdRecordRepository inventoryholdRecordRepository;
    
    @Mock
    private InventoryDiffQueryRepository inventoryDiffQueryRepository;
    
    @Mock
    private PendingPushAliRecordRepository pendingPushAliRecordRepository;
    
    @Mock
    private MslServiceLifeRepository mslServiceLifeRepository;
    
    @Mock
    private ZteStockInfoUploadRepository zteStockInfoUploadRepository;
    
    @Mock
    private ImesCenterfactoryRemoteService imesCenterfactoryRemoteService;

    private SysLookupValuesDTO mockSysLookupValuesDTO;
    private List<ZteWarehouseInfoDTO> mockWarehouseList;
    private List<PendingPushAliRecord> mockSyncData;
    private List<PendingPushAliRecord> mockPendingRecords;
    private List<PendingPushAliRecordDTO> mockPushAliRecordDTOList;

    @Before
    public void setUp() {
        // 初始化基础mock数据
        mockSysLookupValuesDTO = new SysLookupValuesDTO();
        mockSysLookupValuesDTO.setLookupMeaning("TEST_MESSAGE_TYPE");
        
        mockWarehouseList = Arrays.asList(createMockWarehouseInfo("WH001"));
        
        // 设置mock同步数据 - 包含入库和出库记录
        mockSyncData = Arrays.asList(
            createMockPendingRecord(1L, PENDINGIN),
            createMockPendingRecord(2L, PENDINGOUT)
        );
        
        // 设置mock待推送记录 - 每种类型使用相同的messageId以确保按messageId分组时行为一致
        mockPendingRecords = Arrays.asList(
            createMockPendingRecordWithMessageId(1L, PENDINGIN, "msg1"),
            createMockPendingRecordWithMessageId(2L, PENDINGIN, "msg1"), // 同一个messageId的入库记录
            createMockPendingRecordWithMessageId(3L, PENDINGOUT, "msg2"),
            createMockPendingRecordWithMessageId(4L, PENDINGOUT, "msg2") // 同一个messageId的出库记录
        );
        
        // 设置mock推送记录详情
        mockPushAliRecordDTOList = Arrays.asList(createMockPushAliRecordDTO());
    }

    @Test
    public void testPushPendingRecordJob_Success() throws Exception {
        // Mock Redis序列号生成
        PowerMockito.mockStatic(RedisSerialNoUtil.class);
        PowerMockito.when(RedisSerialNoUtil.getDateIncreaseId(anyString(), anyInt()))
            .thenReturn("ZTE101_Create_Transfer_20240101001")
            .thenReturn("ZTE101_Move_20240101001");
        
        // 模拟createPendingRecord成功场景
        when(inventoryholdRecordRepository.getLookupValue(LOOKUP_CODE_100010300001))
            .thenReturn(mockSysLookupValuesDTO);
        when(inventoryDiffQueryRepository.getInforWarehouseList())
            .thenReturn(mockWarehouseList);
        when(pendingPushAliRecordRepository.selectSyncData(any(), any()))
            .thenReturn(mockSyncData);
        when(inventoryholdRecordRepository.getLookupValue(LOOKUP_CODE_100009100002))
            .thenReturn(mockSysLookupValuesDTO);
        
        // 模拟pushDataToAliB2B成功场景
        when(pendingPushAliRecordRepository.selectWaitPushPendingRecord())
            .thenReturn(mockPendingRecords);
        when(pendingPushAliRecordRepository.selectPushAliData(anyList(), anyList()))
            .thenReturn(mockPushAliRecordDTOList);
        
        // Mock pushDataToB2B方法返回true
        ZteAlibabaServiceImpl spyService = spy(zteAlibabaService);
        doReturn(true).when(spyService).pushDataToB2B(anyString(), any(), anyString());

        // 执行测试
        spyService.pushPendingRecordJob();

        // 验证方法调用
        verify(inventoryholdRecordRepository, times(2)).getLookupValue(anyString());
        verify(inventoryDiffQueryRepository).getInforWarehouseList();
        verify(pendingPushAliRecordRepository).selectSyncData(any(), any());
        verify(pendingPushAliRecordRepository, times(2)).batchInsertPendingPushAliRecord(any());
        verify(mslServiceLifeRepository).updateLookupValues(any());
        verify(pendingPushAliRecordRepository).selectWaitPushPendingRecord();
        verify(pendingPushAliRecordRepository, times(1)).batchUpdateSendStatus(anyList(), eq(INT_0), eq(false));
        verify(pendingPushAliRecordRepository, times(2)).selectPushAliData(anyList(), anyList());
        verify(spyService, times(2)).pushDataToB2B(anyString(), any(), anyString());
        verify(pendingPushAliRecordRepository, times(2)).batchUpdateSendStatus(anyList(), eq(INT_0), eq(true));
    }

    @Test
    public void testCreatePendingRecord_GetExecuteDateNull() {
        // 测试获取执行时间为null的分支
        when(inventoryholdRecordRepository.getLookupValue(LOOKUP_CODE_100010300001))
            .thenReturn(null);
        when(inventoryDiffQueryRepository.getInforWarehouseList())
            .thenReturn(mockWarehouseList);
        when(pendingPushAliRecordRepository.selectSyncData(any(), isNull()))
            .thenReturn(mockSyncData);
        when(inventoryholdRecordRepository.getLookupValue(LOOKUP_CODE_100009100002))
            .thenReturn(mockSysLookupValuesDTO);

        ZteAlibabaServiceImpl spyService = spy(zteAlibabaService);
        
        // 执行测试
        spyService.pushPendingRecordJob();

        // 验证executeDate为null时的调用
        verify(pendingPushAliRecordRepository).selectSyncData(any(), isNull());
    }

    @Test
    public void testCreatePendingRecord_EmptyWarehouseList() {
        // 测试仓库列表为空的分支
        when(inventoryholdRecordRepository.getLookupValue(LOOKUP_CODE_100010300001))
            .thenReturn(mockSysLookupValuesDTO);
        when(inventoryDiffQueryRepository.getInforWarehouseList())
            .thenReturn(Collections.emptyList());

        ZteAlibabaServiceImpl spyService = spy(zteAlibabaService);
        
        // 执行测试
        spyService.pushPendingRecordJob();

        // 验证当仓库列表为空时，不执行后续操作
        verify(pendingPushAliRecordRepository, never()).selectSyncData(any(), any());
        verify(pendingPushAliRecordRepository, never()).batchInsertPendingPushAliRecord(any());
    }

    @Test(expected = BusiException.class)
    public void testCreatePendingRecord_MessageTypeDtoNotFound() {
        // 测试消息类型DTO未找到时抛出异常
        when(inventoryholdRecordRepository.getLookupValue(LOOKUP_CODE_100010300001))
            .thenReturn(mockSysLookupValuesDTO);
        when(inventoryDiffQueryRepository.getInforWarehouseList())
            .thenReturn(mockWarehouseList);
        when(pendingPushAliRecordRepository.selectSyncData(any(), any()))
            .thenReturn(mockSyncData);
        when(inventoryholdRecordRepository.getLookupValue(LOOKUP_CODE_100009100002))
            .thenReturn(null);

        // 执行测试并验证异常
        zteAlibabaService.pushPendingRecordJob();
    }

    @Test
    public void testBatchSavePendingRecords_EmptyRecords() {
        // 测试记录列表为空的分支
        when(inventoryholdRecordRepository.getLookupValue(LOOKUP_CODE_100010300001))
            .thenReturn(mockSysLookupValuesDTO);
        when(inventoryDiffQueryRepository.getInforWarehouseList())
            .thenReturn(mockWarehouseList);
        when(pendingPushAliRecordRepository.selectSyncData(any(), any()))
            .thenReturn(Collections.emptyList());
        when(inventoryholdRecordRepository.getLookupValue(LOOKUP_CODE_100009100002))
            .thenReturn(mockSysLookupValuesDTO);

        ZteAlibabaServiceImpl spyService = spy(zteAlibabaService);
        
        // 执行测试
        spyService.pushPendingRecordJob();

        // 验证空记录时不执行批量插入
        verify(pendingPushAliRecordRepository, never()).batchInsertPendingPushAliRecord(any());
    }

    @Test
    public void testBatchSavePendingRecords_FilteredRecordsEmpty() {
        // 测试过滤后记录为空的分支 - 没有匹配的记录类型
        List<PendingPushAliRecord> otherTypeRecords = Arrays.asList(
            createMockPendingRecord(1L, "OTHER_TYPE")
        );
        
        when(inventoryholdRecordRepository.getLookupValue(LOOKUP_CODE_100010300001))
            .thenReturn(mockSysLookupValuesDTO);
        when(inventoryDiffQueryRepository.getInforWarehouseList())
            .thenReturn(mockWarehouseList);
        when(pendingPushAliRecordRepository.selectSyncData(any(), any()))
            .thenReturn(otherTypeRecords);
        when(inventoryholdRecordRepository.getLookupValue(LOOKUP_CODE_100009100002))
            .thenReturn(mockSysLookupValuesDTO);

        ZteAlibabaServiceImpl spyService = spy(zteAlibabaService);
        
        // 执行测试
        spyService.pushPendingRecordJob();

        // 验证过滤后为空时不执行批量插入
        verify(pendingPushAliRecordRepository, never()).batchInsertPendingPushAliRecord(any());
    }

    @Test
    public void testPushDataToAliB2B_EmptyPendingRecords() {
        // 测试待推送记录为空的分支
        when(inventoryholdRecordRepository.getLookupValue(LOOKUP_CODE_100010300001))
            .thenReturn(mockSysLookupValuesDTO);
        when(inventoryDiffQueryRepository.getInforWarehouseList())
            .thenReturn(mockWarehouseList);
        when(pendingPushAliRecordRepository.selectSyncData(any(), any()))
            .thenReturn(mockSyncData);
        when(inventoryholdRecordRepository.getLookupValue(LOOKUP_CODE_100009100002))
            .thenReturn(mockSysLookupValuesDTO);
        when(pendingPushAliRecordRepository.selectWaitPushPendingRecord())
            .thenReturn(Collections.emptyList());

        ZteAlibabaServiceImpl spyService = spy(zteAlibabaService);
        
        // 执行测试
        spyService.pushPendingRecordJob();

        // 验证待推送记录为空时不执行后续操作
        verify(pendingPushAliRecordRepository, never()).batchUpdateSendStatus(anyList(), anyInt(), anyBoolean());
        verify(spyService, never()).pushDataToB2B(anyString(), any(), anyString());
    }

    @Test
    public void testPushPendingRecordJob_PushDataToB2BFailure() throws Exception {
        // Mock Redis序列号生成
        PowerMockito.mockStatic(RedisSerialNoUtil.class);
        PowerMockito.when(RedisSerialNoUtil.getDateIncreaseId(anyString(), anyInt()))
            .thenReturn("ZTE101_Create_Transfer_20240101001")
            .thenReturn("ZTE101_Move_20240101001");
        
        // 测试pushDataToB2B返回false的场景
        when(inventoryholdRecordRepository.getLookupValue(LOOKUP_CODE_100010300001))
            .thenReturn(mockSysLookupValuesDTO);
        when(inventoryDiffQueryRepository.getInforWarehouseList())
            .thenReturn(mockWarehouseList);
        when(pendingPushAliRecordRepository.selectSyncData(any(), any()))
            .thenReturn(mockSyncData);
        when(inventoryholdRecordRepository.getLookupValue(LOOKUP_CODE_100009100002))
            .thenReturn(mockSysLookupValuesDTO);
        when(pendingPushAliRecordRepository.selectWaitPushPendingRecord())
            .thenReturn(mockPendingRecords);
        when(pendingPushAliRecordRepository.selectPushAliData(anyList(), anyList()))
            .thenReturn(mockPushAliRecordDTOList);

        ZteAlibabaServiceImpl spyService = spy(zteAlibabaService);
        doReturn(false).when(spyService).pushDataToB2B(anyString(), any(), anyString());

        // 执行测试
        spyService.pushPendingRecordJob();

        // 验证失败时更新状态为-1
        verify(pendingPushAliRecordRepository, times(2)).batchUpdateSendStatus(anyList(), eq(INT_MINUS_1), eq(true));
    }

    @Test
    public void testLargeDataBatch_Over500Records() throws Exception {
        // Mock Redis序列号生成
        PowerMockito.mockStatic(RedisSerialNoUtil.class);
        PowerMockito.when(RedisSerialNoUtil.getDateIncreaseId(anyString(), anyInt()))
            .thenReturn("ZTE101_Create_Transfer_20240101001", "ZTE101_Move_20240101001");
        
        // 测试大数据量分批处理（超过500条记录）
        List<PendingPushAliRecord> largeSyncData = new ArrayList<>();
        List<PendingPushAliRecord> largePendingRecords = new ArrayList<>();
        
        // 创建超过500条记录
        for (int i = 1; i <= 600; i++) {
            largeSyncData.add(createMockPendingRecord((long) i, i % 2 == 0 ? PENDINGIN : PENDINGOUT));
            largePendingRecords.add(createMockPendingRecordWithMessageId((long) i, i % 2 == 0 ? PENDINGIN : PENDINGOUT, "msg" + i));
        }
        
        when(inventoryholdRecordRepository.getLookupValue(LOOKUP_CODE_100010300001))
            .thenReturn(mockSysLookupValuesDTO);
        when(inventoryDiffQueryRepository.getInforWarehouseList())
            .thenReturn(mockWarehouseList);
        when(pendingPushAliRecordRepository.selectSyncData(any(), any()))
            .thenReturn(largeSyncData);
        when(inventoryholdRecordRepository.getLookupValue(LOOKUP_CODE_100009100002))
            .thenReturn(mockSysLookupValuesDTO);
        when(pendingPushAliRecordRepository.selectWaitPushPendingRecord())
            .thenReturn(largePendingRecords);
        when(pendingPushAliRecordRepository.selectPushAliData(anyList(), anyList()))
            .thenReturn(mockPushAliRecordDTOList);

        ZteAlibabaServiceImpl spyService = spy(zteAlibabaService);
        doReturn(true).when(spyService).pushDataToB2B(anyString(), any(), anyString());

        // 执行测试
        spyService.pushPendingRecordJob();

        // 验证分批处理 - 应该有多次调用batchInsertPendingPushAliRecord（因为超过500条会分批）
        verify(pendingPushAliRecordRepository, atLeast(2)).batchInsertPendingPushAliRecord(any());
        verify(pendingPushAliRecordRepository, atLeast(2)).batchUpdateSendStatus(anyList(), eq(INT_0), eq(false));
    }

    // 辅助方法
    private ZteWarehouseInfoDTO createMockWarehouseInfo(String whseId) {
        ZteWarehouseInfoDTO dto = new ZteWarehouseInfoDTO();
        dto.setWarehouseId(whseId);
        return dto;
    }

    private PendingPushAliRecord createMockPendingRecord(Long recordId, String recordType) {
        PendingPushAliRecord record = new PendingPushAliRecord();
        record.setRecordId(recordId);
        record.setRecordType(recordType);
        record.setWhseId("WH001");
        return record;
    }

    private PendingPushAliRecord createMockPendingRecordWithMessageId(Long recordId, String recordType, String messageId) {
        PendingPushAliRecord record = createMockPendingRecord(recordId, recordType);
        record.setMessageId(messageId);
        return record;
    }

    private PendingPushAliRecordDTO createMockPushAliRecordDTO() {
        PendingPushAliRecordDTO dto = new PendingPushAliRecordDTO();
        dto.setRecordId(1L);
        dto.setItrnSeriallKey(12345L);
        dto.setMessageType("TEST_MESSAGE_TYPE");
        dto.setItrnQty(new BigDecimal("10"));
        dto.setLpnType("ORIGINAL");
        dto.setLpn("LPN001");
        dto.setSn("SN001");
        return dto;
    }
}
