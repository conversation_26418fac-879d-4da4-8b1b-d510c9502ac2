package com.zte.application.infor;

import com.zte.application.infor.impl.PendingPushAliRecordServiceImpl;
import com.zte.domain.model.infor.InventoryDiffQueryRepository;
import com.zte.domain.model.infor.PendingPushAliRecordRepository;
import com.zte.interfaces.infor.dto.PendingQualityCheckTaskDTO;
import com.zte.interfaces.infor.dto.PendingQualityCheckTaskListVO;
import com.zte.interfaces.step.dto.ZteWarehouseInfoDTO;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.*;

/**
 * PendingPushAliRecordServiceImpl 单元测试类
 * 目标：达到100%分支覆盖率
 */
@RunWith(PowerMockRunner.class)
public class PendingPushAliRecordServiceImplTest {

    @InjectMocks
    private PendingPushAliRecordServiceImpl pendingPushAliRecordService;
    @Mock
    private PendingPushAliRecordRepository pendingPushAliRecordRepository;
    @Mock
    private InventoryDiffQueryRepository inventoryDiffQueryRepository;

    @Test
    public void getPendingQualityCheckTaskList() throws Exception {
        PowerMockito.when(inventoryDiffQueryRepository.getInforWarehouseList()).thenReturn(new ArrayList<>());
        PendingQualityCheckTaskDTO dto = new PendingQualityCheckTaskDTO();
        dto.setPageIndex(1);
        dto.setPageSize(10);
        PendingQualityCheckTaskListVO vo = pendingPushAliRecordService.getPendingQualityCheckTaskList(dto);
        Assert.assertNotNull(vo);

        List<ZteWarehouseInfoDTO> zteWarehouseList = new ArrayList<>();
        ZteWarehouseInfoDTO zteWarehouseInfoDTO = new ZteWarehouseInfoDTO();
        zteWarehouseList.add(zteWarehouseInfoDTO);
        PowerMockito.when(inventoryDiffQueryRepository.getInforWarehouseList()).thenReturn(zteWarehouseList);
        vo = pendingPushAliRecordService.getPendingQualityCheckTaskList(dto);
        Assert.assertNotNull(vo);

        dto.setWhseid("WHSE227");
        vo = pendingPushAliRecordService.getPendingQualityCheckTaskList(dto);
        Assert.assertNotNull(vo);
    }
}
