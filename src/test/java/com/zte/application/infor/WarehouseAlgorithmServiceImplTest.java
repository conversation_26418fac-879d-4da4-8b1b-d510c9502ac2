package com.zte.application.infor;

import cn.hutool.core.io.FileUtil;
import com.alibaba.fastjson.JSON;
import com.zte.application.infor.impl.WarehouseAlgorithmServiceImpl;
import com.zte.domain.model.infor.InventoryholdRecordRepository;
import com.zte.domain.model.infor.WarehouseAlgorithmRepository;
import com.zte.interfaces.infor.dto.*;
import com.zte.iss.approval.sdk.client.ApprovalFlowClient;
import com.zte.itp.msa.core.util.SpringContextUtil;
import com.zte.resourcewarehouse.common.file.CloudDiskHelper;
import com.zte.resourcewarehouse.common.remoteservice.RemoteServiceDataUtil;
import com.zte.resourcewarehouse.common.utils.*;
import org.apache.commons.lang3.StringUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.reflect.Whitebox;
import org.springframework.beans.BeanUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.Executor;

import static com.zte.common.utils.Constant.*;

/**
 * <AUTHOR>
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({BusiAssertException.class,CommonUtils.class,RemoteServiceDataUtil.class,Executor.class,ExcelUtil.class,EmailUtil.class, FileUtil.class,
        SpringContextUtil.class, CloudDiskHelper.class, StringUtils.class,JSON.class,Tools.class, ApprovalFlowClient.class, BeanUtils.class})
public class WarehouseAlgorithmServiceImplTest {

    @InjectMocks
    private WarehouseAlgorithmServiceImpl warehouseAlgorithmService;
    @Mock
    private InventoryholdRecordRepository inventoryholdRecordRepository;
    @Mock
    private WarehouseAlgorithmRepository warehouseAlgorithmRepository;

    @Before
    public void init() throws Exception{
        PowerMockito.mockStatic(BusiAssertException.class,CommonUtils.class,RemoteServiceDataUtil.class,EmailUtil.class,FileUtil.class,
                SpringContextUtil.class, CloudDiskHelper.class, StringUtils.class, ApprovalFlowClient.class, BeanUtils.class);
    }
    @Test
    public void getSchemeTypeList() throws Exception {

        SysLookupValuesDTO sysLookupValuesDTO = SysLookupValuesDTO.builder().build().setLookupType(LOOKUP_TYPE_1000060);
        PowerMockito.when(inventoryholdRecordRepository.getLookupValues(Mockito.any())).thenReturn(Mockito.any());
        Whitebox.invokeMethod(warehouseAlgorithmService, "getSchemeTypeList");

        Assert.assertNotNull(sysLookupValuesDTO);
    }

    @Test
    public void getWholeWarehouseStrategyList() throws Exception {

        SysLookupValuesDTO sysLookupValuesDTO = SysLookupValuesDTO.builder().build().setLookupType(LOOKUP_TYPE_1000061);
        PowerMockito.when(inventoryholdRecordRepository.getLookupValues(Mockito.any())).thenReturn(Mockito.any());
        Whitebox.invokeMethod(warehouseAlgorithmService, "getWholeWarehouseStrategyList");

        Assert.assertNotNull(sysLookupValuesDTO);
    }

    @Test
    public void getWarehouseAlgorithmList() throws Exception {

        WarehouseAlgorithmDTO dto = new WarehouseAlgorithmDTO();
        dto.setPageIndex(1);
        dto.setPageSize(10);
        PowerMockito.when(warehouseAlgorithmRepository.getWarehouseAlgorithmListVOTotal(Mockito.any())).thenReturn(1);
        PowerMockito.when(warehouseAlgorithmRepository.getWarehouseAlgorithmList(Mockito.any())).thenReturn(Mockito.any());
        Whitebox.invokeMethod(warehouseAlgorithmService, "getWarehouseAlgorithmList", dto);

        Assert.assertNotNull(dto);
    }

    @Test
    public void saveWarehouseAlgorithm() throws Exception {

        WarehouseAlgorithmDTO dto = new WarehouseAlgorithmDTO();
        dto.setWhseid("WMWHSE1");
        dto.setAlgorithmScheme("1");

        PowerMockito.when(warehouseAlgorithmRepository.getWarehouseAlgorithmListVOTotal(Mockito.any())).thenReturn(1);
        Whitebox.invokeMethod(warehouseAlgorithmService, "saveWarehouseAlgorithm", dto);

        PowerMockito.when(warehouseAlgorithmRepository.getWarehouseAlgorithmListVOTotal(Mockito.any())).thenReturn(0);
        List<WarehouseAlgorithmDTO> warehouseAlgorithmDTOS = new ArrayList<>();
        PowerMockito.when(warehouseAlgorithmRepository.getWarehouseAlgorithmList(Mockito.any())).thenReturn(warehouseAlgorithmDTOS);
        PowerMockito.when(warehouseAlgorithmRepository.saveWarehouseAlgorithm(Mockito.any())).thenReturn(1);
        Whitebox.invokeMethod(warehouseAlgorithmService, "saveWarehouseAlgorithm", dto);

        WarehouseAlgorithmDTO warehouseAlgorithmDTO = new WarehouseAlgorithmDTO();
        warehouseAlgorithmDTO.setAlgorithmScheme("1");
        warehouseAlgorithmDTO.setSerialkey(BigDecimal.ONE);
        warehouseAlgorithmDTOS.add(warehouseAlgorithmDTO);
        PowerMockito.when(warehouseAlgorithmRepository.getWarehouseAlgorithmList(Mockito.any())).thenReturn(warehouseAlgorithmDTOS);
        PowerMockito.when(warehouseAlgorithmRepository.saveWarehouseAlgorithm(Mockito.any())).thenReturn(1);
        Whitebox.invokeMethod(warehouseAlgorithmService, "checkAlgorithmExists", dto, warehouseAlgorithmDTO);
        Whitebox.invokeMethod(warehouseAlgorithmService, "saveWarehouseAlgorithm", dto);

        dto.setSerialkey(BigDecimal.ONE);
        PowerMockito.when(warehouseAlgorithmRepository.saveWarehouseAlgorithm(Mockito.any())).thenReturn(1);
        Whitebox.invokeMethod(warehouseAlgorithmService, "saveWarehouseAlgorithm", dto);

        Assert.assertNotNull(dto);
    }

    @Test
    public void checkAlgorithmExists() throws Exception {

        WarehouseAlgorithmDTO dto = new WarehouseAlgorithmDTO();
        dto.setAlgorithmScheme("1");
        dto.setSchemeType("1");
        WarehouseAlgorithmDTO existsDto = new WarehouseAlgorithmDTO();
        existsDto.setAlgorithmScheme("1");
        existsDto.setSchemeType("2");
        Whitebox.invokeMethod(warehouseAlgorithmService, "checkAlgorithmExists", dto, existsDto);

        existsDto.setSchemeType("1");
        dto.setWholeWarehouseStrategy("1");
        existsDto.setWholeWarehouseStrategy("2");
        Whitebox.invokeMethod(warehouseAlgorithmService, "checkAlgorithmExists", dto, existsDto);

        existsDto.setWholeWarehouseStrategy("1");
        dto.setMaxWarehouseAdjust(BigDecimal.ONE);
        existsDto.setMaxWarehouseAdjust(BigDecimal.ZERO);
        Whitebox.invokeMethod(warehouseAlgorithmService, "checkAlgorithmExists", dto, existsDto);

        existsDto.setMaxWarehouseAdjust(BigDecimal.ONE);
        dto.setUnitWholeWarehouseWork(BigDecimal.ONE);
        existsDto.setUnitWholeWarehouseWork(BigDecimal.ZERO);
        Whitebox.invokeMethod(warehouseAlgorithmService, "checkAlgorithmExists", dto, existsDto);

        existsDto.setUnitWholeWarehouseWork(BigDecimal.ONE);
        dto.setStartCoordX(BigDecimal.ONE);
        existsDto.setStartCoordX(BigDecimal.ZERO);
        Whitebox.invokeMethod(warehouseAlgorithmService, "checkAlgorithmExists", dto, existsDto);

        existsDto.setStartCoordX(BigDecimal.ONE);
        dto.setStartCoordY(BigDecimal.ONE);
        existsDto.setStartCoordY(BigDecimal.ZERO);
        Whitebox.invokeMethod(warehouseAlgorithmService, "checkAlgorithmExists", dto, existsDto);

        existsDto.setStartCoordY(BigDecimal.ONE);
        dto.setEndCoordX(BigDecimal.ONE);
        existsDto.setEndCoordX(BigDecimal.ZERO);
        Whitebox.invokeMethod(warehouseAlgorithmService, "checkAlgorithmExists", dto, existsDto);

        existsDto.setEndCoordX(BigDecimal.ONE);
        dto.setEndCoordY(BigDecimal.ONE);
        existsDto.setEndCoordY(BigDecimal.ZERO);
        Whitebox.invokeMethod(warehouseAlgorithmService, "checkAlgorithmExists", dto, existsDto);

        existsDto.setEndCoordY(BigDecimal.ONE);
        dto.setExecutionFrequency(BigDecimal.ONE);
        existsDto.setExecutionFrequency(BigDecimal.ZERO);
        Whitebox.invokeMethod(warehouseAlgorithmService, "checkAlgorithmExists", dto, existsDto);

        existsDto.setExecutionFrequency(BigDecimal.ONE);
        dto.setWholeWarehouseManpower(BigDecimal.ONE);
        existsDto.setWholeWarehouseManpower(BigDecimal.ZERO);
        Whitebox.invokeMethod(warehouseAlgorithmService, "checkAlgorithmExists", dto, existsDto);

        existsDto.setExecutionFrequency(BigDecimal.ONE);
        dto.setMinWholeWarehouseUnit(BigDecimal.ONE);
        existsDto.setMinWholeWarehouseUnit(BigDecimal.ZERO);
        Whitebox.invokeMethod(warehouseAlgorithmService, "checkAlgorithmExists", dto, existsDto);

        existsDto.setMinWholeWarehouseUnit(BigDecimal.ONE);
        Whitebox.invokeMethod(warehouseAlgorithmService, "checkAlgorithmExists", dto, existsDto);

        Assert.assertNotNull(dto);
    }

    @Test
    public void updateWarehouseAlgorithm() throws Exception {

        WarehouseAlgorithmDTO dto = new WarehouseAlgorithmDTO();
        dto.setSerialkey(BigDecimal.ONE);
        dto.setEnabledFlag(FLAG_N);
        PowerMockito.when(warehouseAlgorithmRepository.updateWarehouseAlgorithm(Mockito.any())).thenReturn(1);
        Whitebox.invokeMethod(warehouseAlgorithmService, "updateWarehouseAlgorithm", dto);

        dto.setEnabledFlag(FLAG_Y);
        List<WarehouseAlgorithmDTO> list = new ArrayList<>();
        WarehouseAlgorithmDTO warehouseAlgorithmDTO = new WarehouseAlgorithmDTO();
        warehouseAlgorithmDTO.setSerialkey(BigDecimal.ONE);
        list.add(warehouseAlgorithmDTO);
        PowerMockito.when(warehouseAlgorithmRepository.getWarehouseAlgorithmList(Mockito.any())).thenReturn(list);
        PowerMockito.when(warehouseAlgorithmRepository.updateWarehouseAlgorithm(Mockito.any())).thenReturn(1);
        Whitebox.invokeMethod(warehouseAlgorithmService, "updateWarehouseAlgorithm", dto);

        WarehouseAlgorithmDTO warehouseAlgorithmDTO1 = new WarehouseAlgorithmDTO();
        warehouseAlgorithmDTO1.setSerialkey(BigDecimal.ZERO);
        list.add(warehouseAlgorithmDTO1);
        PowerMockito.when(warehouseAlgorithmRepository.getWarehouseAlgorithmList(Mockito.any())).thenReturn(list);
        Whitebox.invokeMethod(warehouseAlgorithmService, "checkAlgorithmExists", dto, warehouseAlgorithmDTO);
        PowerMockito.when(warehouseAlgorithmRepository.updateWarehouseAlgorithm(Mockito.any())).thenReturn(1);
        Whitebox.invokeMethod(warehouseAlgorithmService, "updateWarehouseAlgorithm", dto);

        Assert.assertNotNull(dto);
    }

    @Test
    public void importWarehouseAlgorithm() throws Exception {

        WarehouseAlgorithmDTO dto = new WarehouseAlgorithmDTO();
        dto.setAlgorithmScheme("1");

        List<WarehouseAlgorithmDTO> list = new ArrayList<>();
        Whitebox.invokeMethod(warehouseAlgorithmService, "importWarehouseAlgorithm", list);

        list.add(dto);
        WarehouseAlgorithmDTO dto2 = new WarehouseAlgorithmDTO();
        dto2.setAlgorithmScheme("1");
        list.add(dto2);
        Whitebox.invokeMethod(warehouseAlgorithmService, "importWarehouseAlgorithm", list);

        list.clear();
        list.add(dto);
        PowerMockito.when(warehouseAlgorithmRepository.getWarehouseAlgorithmCount(Mockito.any())).thenReturn(1);
        Whitebox.invokeMethod(warehouseAlgorithmService, "importWarehouseAlgorithm", list);

        PowerMockito.when(warehouseAlgorithmRepository.getWarehouseAlgorithmCount(Mockito.any())).thenReturn(0);
        List<WarehouseAlgorithmDTO> algorithmDTOList = new ArrayList<>();
        WarehouseAlgorithmDTO warehouseAlgorithmDTO = new WarehouseAlgorithmDTO();
        warehouseAlgorithmDTO.setAlgorithmScheme("2");
        algorithmDTOList.add(warehouseAlgorithmDTO);
        Whitebox.invokeMethod(warehouseAlgorithmService, "checkAlgorithm", algorithmDTOList);
        List<WarehouseAlgorithmDTO> warehouseAlgorithmDTOList = new ArrayList<>();
        warehouseAlgorithmDTOList.add(warehouseAlgorithmDTO);
        PowerMockito.when(warehouseAlgorithmRepository.getWarehouseAlgorithmList(Mockito.any())).thenReturn(warehouseAlgorithmDTOList);
        PowerMockito.when(warehouseAlgorithmRepository.saveWarehouseAlgorithmList(Mockito.any())).thenReturn(1);
        Whitebox.invokeMethod(warehouseAlgorithmService, "importWarehouseAlgorithm", list);

        WarehouseAlgorithmDTO warehouseAlgorithmDTO1 = new WarehouseAlgorithmDTO();
        warehouseAlgorithmDTO1.setAlgorithmScheme("1");
        warehouseAlgorithmDTOList.add(warehouseAlgorithmDTO1);
        PowerMockito.when(warehouseAlgorithmRepository.getWarehouseAlgorithmList(Mockito.any())).thenReturn(warehouseAlgorithmDTOList);
        PowerMockito.when(warehouseAlgorithmRepository.saveWarehouseAlgorithmList(Mockito.any())).thenReturn(1);
        Whitebox.invokeMethod(warehouseAlgorithmService, "importWarehouseAlgorithm", list);

        Assert.assertNotNull(dto);
    }

    @Test
    public void checkAlgorithm() throws Exception {
        List<WarehouseAlgorithmDTO> algorithmDTOList = new ArrayList<>();
        WarehouseAlgorithmDTO dto = new WarehouseAlgorithmDTO();
        dto.setAlgorithmScheme("1");
        dto.setSchemeType("1");
        algorithmDTOList.add(dto);
        WarehouseAlgorithmDTO warehouseAlgorithmDTO = new WarehouseAlgorithmDTO();
        warehouseAlgorithmDTO.setAlgorithmScheme("1");
        warehouseAlgorithmDTO.setSchemeType("2");
        algorithmDTOList.add(warehouseAlgorithmDTO);
        Whitebox.invokeMethod(warehouseAlgorithmService, "checkAlgorithm", algorithmDTOList);

        algorithmDTOList.clear();
        warehouseAlgorithmDTO.setSchemeType("1");
        dto.setWholeWarehouseStrategy("1");
        warehouseAlgorithmDTO.setWholeWarehouseStrategy("2");
        algorithmDTOList.add(dto);
        algorithmDTOList.add(warehouseAlgorithmDTO);
        Whitebox.invokeMethod(warehouseAlgorithmService, "checkAlgorithm", algorithmDTOList);

        algorithmDTOList.clear();
        warehouseAlgorithmDTO.setWholeWarehouseStrategy("1");
        dto.setMaxWarehouseAdjust(BigDecimal.ONE);
        warehouseAlgorithmDTO.setMaxWarehouseAdjust(BigDecimal.ZERO);
        algorithmDTOList.add(dto);
        algorithmDTOList.add(warehouseAlgorithmDTO);
        Whitebox.invokeMethod(warehouseAlgorithmService, "checkAlgorithm", algorithmDTOList);

        algorithmDTOList.clear();
        warehouseAlgorithmDTO.setMaxWarehouseAdjust(BigDecimal.ONE);
        dto.setUnitWholeWarehouseWork(BigDecimal.ONE);
        warehouseAlgorithmDTO.setUnitWholeWarehouseWork(BigDecimal.ZERO);
        algorithmDTOList.add(dto);
        algorithmDTOList.add(warehouseAlgorithmDTO);
        Whitebox.invokeMethod(warehouseAlgorithmService, "checkAlgorithm", algorithmDTOList);

        algorithmDTOList.clear();
        warehouseAlgorithmDTO.setUnitWholeWarehouseWork(BigDecimal.ONE);
        dto.setStartCoordX(BigDecimal.ONE);
        warehouseAlgorithmDTO.setStartCoordX(BigDecimal.ZERO);
        algorithmDTOList.add(dto);
        algorithmDTOList.add(warehouseAlgorithmDTO);
        Whitebox.invokeMethod(warehouseAlgorithmService, "checkAlgorithm", algorithmDTOList);

        algorithmDTOList.clear();
        warehouseAlgorithmDTO.setStartCoordX(BigDecimal.ONE);
        dto.setStartCoordY(BigDecimal.ONE);
        warehouseAlgorithmDTO.setStartCoordY(BigDecimal.ZERO);
        algorithmDTOList.add(dto);
        algorithmDTOList.add(warehouseAlgorithmDTO);
        Whitebox.invokeMethod(warehouseAlgorithmService, "checkAlgorithm", algorithmDTOList);

        algorithmDTOList.clear();
        warehouseAlgorithmDTO.setStartCoordY(BigDecimal.ONE);
        dto.setEndCoordX(BigDecimal.ONE);
        warehouseAlgorithmDTO.setEndCoordX(BigDecimal.ZERO);
        algorithmDTOList.add(dto);
        algorithmDTOList.add(warehouseAlgorithmDTO);
        Whitebox.invokeMethod(warehouseAlgorithmService, "checkAlgorithm", algorithmDTOList);

        algorithmDTOList.clear();
        warehouseAlgorithmDTO.setEndCoordX(BigDecimal.ONE);
        dto.setEndCoordY(BigDecimal.ONE);
        warehouseAlgorithmDTO.setEndCoordY(BigDecimal.ZERO);
        algorithmDTOList.add(dto);
        algorithmDTOList.add(warehouseAlgorithmDTO);
        Whitebox.invokeMethod(warehouseAlgorithmService, "checkAlgorithm", algorithmDTOList);

        algorithmDTOList.clear();
        warehouseAlgorithmDTO.setEndCoordY(BigDecimal.ONE);
        dto.setExecutionFrequency(BigDecimal.ONE);
        warehouseAlgorithmDTO.setExecutionFrequency(BigDecimal.ZERO);
        algorithmDTOList.add(dto);
        algorithmDTOList.add(warehouseAlgorithmDTO);
        Whitebox.invokeMethod(warehouseAlgorithmService, "checkAlgorithm", algorithmDTOList);

        algorithmDTOList.clear();
        warehouseAlgorithmDTO.setExecutionFrequency(BigDecimal.ONE);
        dto.setWholeWarehouseManpower(BigDecimal.ONE);
        warehouseAlgorithmDTO.setWholeWarehouseManpower(BigDecimal.ZERO);
        algorithmDTOList.add(dto);
        algorithmDTOList.add(warehouseAlgorithmDTO);
        Whitebox.invokeMethod(warehouseAlgorithmService, "checkAlgorithm", algorithmDTOList);

        algorithmDTOList.clear();
        warehouseAlgorithmDTO.setWholeWarehouseManpower(BigDecimal.ONE);
        dto.setMinWholeWarehouseUnit(BigDecimal.ONE);
        warehouseAlgorithmDTO.setMinWholeWarehouseUnit(BigDecimal.ZERO);
        algorithmDTOList.add(dto);
        algorithmDTOList.add(warehouseAlgorithmDTO);
        Whitebox.invokeMethod(warehouseAlgorithmService, "checkAlgorithm", algorithmDTOList);

        algorithmDTOList.clear();
        warehouseAlgorithmDTO.setMinWholeWarehouseUnit(BigDecimal.ONE);
        algorithmDTOList.add(dto);
        algorithmDTOList.add(warehouseAlgorithmDTO);
        Whitebox.invokeMethod(warehouseAlgorithmService, "checkAlgorithm", algorithmDTOList);

        Assert.assertNotNull(dto);
    }

    @Test
    public void exportWarehouseAlgorithm() throws Exception {

        WarehouseAlgorithmDTO dto = new WarehouseAlgorithmDTO();
        dto.setAlgorithmScheme("1");
        dto.setPageIndex(1);
        dto.setPageSize(10);
        PowerMockito.when(warehouseAlgorithmRepository.getWarehouseAlgorithmListVOTotal(Mockito.any())).thenReturn(1);
        PowerMockito.when(warehouseAlgorithmRepository.getWarehouseAlgorithmList(Mockito.any())).thenReturn(Mockito.any());
        Whitebox.invokeMethod(warehouseAlgorithmService, "exportWarehouseAlgorithm", dto);

        Assert.assertNotNull(dto);
    }

    @Test
    public void getAlgorithmResultHeadList() throws Exception {

        AlgorithmResultDetailDTO dto = new AlgorithmResultDetailDTO();
        dto.setPageIndex(1);
        dto.setPageSize(10);
        PowerMockito.when(warehouseAlgorithmRepository.getAlgorithmResultHeadListVOTotal(Mockito.any())).thenReturn(1);
        PowerMockito.when(warehouseAlgorithmRepository.getAlgorithmResultHeadList(Mockito.any())).thenReturn(Mockito.any());
        Whitebox.invokeMethod(warehouseAlgorithmService, "getAlgorithmResultHeadList", dto);

        Assert.assertNotNull(dto);
    }

    @Test
    public void getAlgorithmResultDetailList() throws Exception {

        AlgorithmResultDetailDTO dto = new AlgorithmResultDetailDTO();
        dto.setPageIndex(1);
        dto.setPageSize(10);
        PowerMockito.when(warehouseAlgorithmRepository.getAlgorithmResultDetailListVOTotal(Mockito.any())).thenReturn(1);
        PowerMockito.when(warehouseAlgorithmRepository.getAlgorithmResultDetailList(Mockito.any())).thenReturn(Mockito.any());
        Whitebox.invokeMethod(warehouseAlgorithmService, "getAlgorithmResultDetailList", dto);

        Assert.assertNotNull(dto);
    }

    @Test
    public void exportAlgorithmResult() throws Exception {

        AlgorithmResultDetailDTO dto = new AlgorithmResultDetailDTO();
        dto.setAlgorithmScheme("1");
        dto.setPageIndex(1);
        dto.setPageSize(10);
        PowerMockito.when(warehouseAlgorithmRepository.getAlgorithmResultDetailListVOTotal(Mockito.any())).thenReturn(1);
        PowerMockito.when(warehouseAlgorithmRepository.getAlgorithmResultDetailList(Mockito.any())).thenReturn(Mockito.any());
        Whitebox.invokeMethod(warehouseAlgorithmService, "exportAlgorithmResult", dto);

        Assert.assertNotNull(dto);
    }

    /* Started by AICoder, pid:a0071gcf7dgd2bb1491e0aa840e8af1bed06decb */
    @Test
    public void getAlgorithmExecuteLogList() throws Exception {

        AlgorithmExecuteLogDTO dto = new AlgorithmExecuteLogDTO();
        dto.setPageIndex(1);
        dto.setPageSize(10);
        PowerMockito.when(warehouseAlgorithmRepository.getAlgorithmExecuteLogListVOTotal(Mockito.any())).thenReturn(1);
        PowerMockito.when(warehouseAlgorithmRepository.getAlgorithmExecuteLogList(Mockito.any())).thenReturn(Mockito.any());
        Whitebox.invokeMethod(warehouseAlgorithmService, "getAlgorithmExecuteLogList", dto);

        Assert.assertNotNull(dto);
    }
    /* Ended by AICoder, pid:a0071gcf7dgd2bb1491e0aa840e8af1bed06decb */

    @Test
    public void getAlgorithmTaskDetailList() {
        AlgorithmResultDetailDTO dto = new AlgorithmResultDetailDTO();
        warehouseAlgorithmService.getAlgorithmTaskDetailList(dto);
        List<AlgorithmTaskDetailDTO> algorithmTaskDetailDTOList = new ArrayList<>();
        AlgorithmTaskDetailDTO algorithmTaskDetailDTO = new AlgorithmTaskDetailDTO();
        algorithmTaskDetailDTO.setAlgorithmScheme("1");
        algorithmTaskDetailDTOList.add(algorithmTaskDetailDTO);
        PowerMockito.when(warehouseAlgorithmRepository.getAlgorithmTaskList(Mockito.any())).thenReturn(algorithmTaskDetailDTOList);
        warehouseAlgorithmService.getAlgorithmTaskDetailList(dto);
        dto.setPageIndex(1);
        dto.setPageSize(1);
        PowerMockito.when(warehouseAlgorithmRepository.getAlgorithmTaskDetailList(Mockito.any())).thenReturn(algorithmTaskDetailDTOList);
        warehouseAlgorithmService.getAlgorithmTaskDetailList(dto);
        Assert.assertNotNull(dto);
    }

    @Test
    public void printAlgorithmTaskInfo() {
        AlgorithmResultDetailDTO dto = new AlgorithmResultDetailDTO();
        warehouseAlgorithmService.printAlgorithmTaskInfo(dto);
        List<AlgorithmResultDetailDTO> algorithmResultDetailList = new ArrayList<>();
        AlgorithmResultDetailDTO algorithmResultDetailDTO = new AlgorithmResultDetailDTO();
        algorithmResultDetailDTO.setStatus("处理");
        algorithmResultDetailList.add(algorithmResultDetailDTO);
        AlgorithmResultDetailDTO algorithmResultDetailDTO1 = new AlgorithmResultDetailDTO();
        algorithmResultDetailDTO1.setStatus("新建");
        algorithmResultDetailList.add(algorithmResultDetailDTO1);
        PowerMockito.when(warehouseAlgorithmRepository.getAlgorithmResultDetailList(Mockito.any())).thenReturn(algorithmResultDetailList);
        warehouseAlgorithmService.printAlgorithmTaskInfo(dto);
        algorithmResultDetailDTO.setStatus("新建");
        warehouseAlgorithmService.printAlgorithmTaskInfo(dto);
        PowerMockito.when(warehouseAlgorithmRepository.getInforInventoryInfo(Mockito.any())).thenReturn(Arrays.asList("1"));
        warehouseAlgorithmService.printAlgorithmTaskInfo(dto);
        Assert.assertNotNull(dto);
    }

}
